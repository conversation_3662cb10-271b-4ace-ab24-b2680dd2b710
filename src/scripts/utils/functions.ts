import { RESPONSE_MESSAGE_STATUS } from "./interfaces";

export function connectWebSocket(wsServerUrl: string) {
  const promise: Promise<WebSocket> = new Promise((resolve, reject) => {
    let socket: WebSocket;
    console.log("Connecting to WebSocket server...");

    socket = new WebSocket(wsServerUrl);

    socket.onopen = () => {
      console.log("WebSocket connection established.");
      resolve(socket);
    };

    socket.onerror = (error) => {
      console.error("WebSocket error:", error);
      reject(error);
    };
  });
  return promise;
}

export async function sendWsRequest(path:string,ws: WebSocket, payload: any) {
  const res = await new Promise<any>((resolve, reject) => {
    const handler = (ev: MessageEvent) => {
      const res = JSON.parse(ev.data);
      if (res.errors) {
        reject(res.errors);
      } else resolve(res.data);
      ws.removeEventListener("message", handler);
    };
    ws.addEventListener("message", handler);
    ws.send(
      JSON.stringify({
        event: path,
        data: payload,
      })
    );
  });
  return res;
}

export async function handleMessageByType(
  request: any,
  bridgeSocket: WebSocket | null,
  timeout = 5000,
  type?: string
) {
  const response: any = {
    status: RESPONSE_MESSAGE_STATUS.SUCCESS,
    type: request.type,
    id: request.id,
    data: null,
  };

  if (bridgeSocket?.readyState === bridgeSocket?.OPEN) {
    const res = await new Promise((resolve, reject) => {
      bridgeSocket?.send(
        JSON.stringify({
          type: request.type ?? type,
          ...request.data,
        })
      );
      bridgeSocket!.onmessage = (event) => {
        const data = JSON.parse(event.data);
        if (data.type === (request.type ?? type)) {
          resolve(data);
        }
      };

      setTimeout(() => {
        reject(null);
      }, timeout);
    });
    response.data = res;
  }

  return response;
}
