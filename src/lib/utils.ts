import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import classNames from "classnames";

export const cx = classNames;

interface Result<T> {
  data?: T;
  error?: Error;
}

export type PromiseResult<T> = Promise<Result<T>>;
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const extractFloats = (s: string) => {
  const regex = /[+-]?(\d[\d,]*)(\.\d+)?/g;
  const matches = s.match(regex);
  if (matches) {
    // Remove commas and convert matched strings to numbers
    return matches.map((m) => parseFloat(m.replace(/,/g, "")));
  } else {
    return [];
  }
};

export const delay = async (time: number): Promise<void> => {
  return new Promise((resolve) => {
    const id = setTimeout(() => {
      resolve();
      clearTimeout(id);
    }, time);
  });
};

export const generateRequestId = () => {
  return Math.random().toString(36).substring(2, 9);
};

export function getHourOffset(
  date1: string | Date,
  date2: string | Date
): number {
  // Ensure both inputs are Date objects
  const d1 = typeof date1 === "string" ? new Date(date1) : date1;
  const d2 = typeof date2 === "string" ? new Date(date2) : date2;

  // Validate the Date objects
  if (isNaN(d1.getTime()) || isNaN(d2.getTime())) {
    throw new Error("Invalid date provided");
  }

  // Calculate the difference in milliseconds
  const diffInMs = Math.abs(d2.getTime() - d1.getTime());

  // Convert milliseconds to hours
  const hourOffset = diffInMs / (1000 * 60 * 60);

  return Math.ceil(hourOffset);
}

export function toLocalISOFormat(date: Date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0"); // Months are 0-based
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");
  const milliseconds = String(date.getMilliseconds()).padStart(3, "0");

  // Construct ISO-like string
  return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.${milliseconds}Z`;
}

export const findWithQuery = (query: string) => {
  return document.querySelector<HTMLElement>(query);
};

export const isPluginEnv = () => {
  return Boolean(window?.chrome?.runtime?.id);
};

export const objectToParams = (obj: Record<string, any>) => {
  var str = "";
  for (var key in obj) {
    if (str != "") {
      str += "&";
    }
    str += key + "=" + encodeURIComponent(obj[key]);
  }
  return str;
};

export const objectToFormData = (obj: Record<string, any>) => {
  const form_data = new FormData();

  for (var key in obj) {
    form_data.append(key, obj[key]);
  }
  return form_data;
};

export const lastElem = <T = any>(arr: T[], diff = 1) => {
  if (!arr?.length) return null;
  return arr[arr.length - diff];
};
