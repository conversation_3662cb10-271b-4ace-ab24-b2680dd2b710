import sessionContext from "@/contexts/session-context";
import { isPluginEnv } from "@/lib/utils";
import Popup from "@/scripts/popup";
import TradeBench from "@/widgets/trade-bench";
import { useEffect, useState } from "react";

const App = () => {
  const [env, setEnv] = useState<"web" | "plugin">();

  useEffect(() => {
    if (isPluginEnv()) setEnv("plugin");
    else setEnv("web");
  }, []);

  return (
    <section className="bg-black w-full h-screen">
      {env == "plugin" && (
        <sessionContext.Provider initialState={{ env: "popup" }}>
          <Popup />
        </sessionContext.Provider>
      )}
      {env == "web" && (
        <sessionContext.Provider initialState={{ env: "web" }}>
          <TradeBench />
        </sessionContext.Provider>
      )}
    </section>
  );
};
export default App;
