import "/node_modules/react-grid-layout/css/styles.css";
import "/node_modules/react-resizable/css/styles.css";

import { Button } from "@/components/ui/button";
import sessionContext from "@/contexts/session-context";
import useFxApi from "@/hooks/use-fx-api";
import { isPluginEnv } from "@/lib/utils";
import Chart, { TradingChartRef } from "@/widgets/trade-panel/chart/chart";
import { Tick } from "@/widgets/trade-panel/utils/interfaces";
import { useEffect, useRef, useState } from "react";
import { Responsive as ResponsiveGridLayout } from "react-grid-layout";

export const rootId = "fx-toolkit-root";
const TradeBench: React.FC<any> = ({}) => {
  const { registerTickListener } = sessionContext.useContainer();
  const { callMethod } = useFxApi();

  const [show, setShow] = useState(false);
  const [width, setWidth] = useState(1200);
  const layoutRef = useRef<ResponsiveGridLayout>(null);
  const chartRef = useRef<TradingChartRef>(null);

  const [staticTicks, setStaticTicks] = useState<Tick[]>([]);
  const [symbols, setSymbols] = useState<string[]>([]);
  const [symbol, setSymbol] = useState<string>("Loading...");
  const [accountName, setAccountName] = useState<string>("Loading...");

  useEffect(() => {
    setWidth(document.body.clientWidth);
    window.addEventListener("resize", () => {
      if (layoutRef.current) {
        setWidth(document.body.clientWidth);
      }
    });
  }, []);

  useEffect(() => {
    if (isPluginEnv()) {
      chrome.runtime.onMessage.addListener((msg, _sender, _sendResponse) => {
        if (msg.cmd === "toggle‑panel") setShow((p) => !p);
      });
    } else {
      setShow(true);
    }
  }, []);

  useEffect(() => {
    const fn = async () => {
      const account = await callMethod("getAccount", {});
      if (account.data) {
        setAccountName(account.data.server);
      }

      const symbols = await callMethod("getSymbols", {});
      if (symbols.data) {
        setSymbols(symbols.data.symbols.map((s) => s.name));
        setSymbol(symbols.data.symbols[0].name);
        await startTicking( symbols.data.symbols[0].name);
        registerTickListener((e) => {
          if (e) chartRef.current?.onTick?.({ price: e.ask, time: e.time });
        });
      }
    };
    fn();
  }, []);

  const startTicking = async (testSymbol = "ETHUSD") => {
    const length = 1000;

    await callMethod("manageSymbol", {
      action: "select",
      symbol: testSymbol,
    });

    const ticks = await callMethod("getTicksFrom", {
      length,
      startDate: Date.now(), // 200 mins
      symbol: testSymbol,
    });

    if (ticks.data && ticks?.data?.ticks?.length > 0) {
      setStaticTicks(
        (ticks.data.ticks ?? []).map((t) => ({ price: t.bid, time: t.time }))
      );
    }
    await callMethod("tickStart", { rate: 1, symbol: testSymbol });
  };

  const killTicking = async () => {
    await callMethod("tickStop", {});
  };

  const onSymbolChange = async (symbol: string) => {
    await killTicking();
    setStaticTicks([]);
    chartRef.current?.reset();
    setSymbol(symbol);
    await callMethod("manageSymbol", {
      action: "select",
      symbol: symbol,
    });
    const ticks = await callMethod("getTicksFrom", {
      length: 1000,
      startDate: Date.now(), // 200 mins
      symbol,
    });
    if (ticks.data && ticks?.data?.ticks?.length > 0) {
      setStaticTicks(
        (ticks.data.ticks ?? []).map((t) => ({ price: t.bid, time: t.time }))
      );
    }
    await callMethod("tickStart", { rate: 1, symbol });
    registerTickListener((e) => {
      if (e) chartRef.current?.onTick?.({ price: e.ask, time: e.time });
    });
  };

  const layout = [
    {
      i: "a",
      x: 2,
      y: 0,
      w: 4,
      h: document.body.clientHeight - 50,
      static: false,
      minW: 2,
      minH: 200,
    },
  ];

  return (
    <section className="w-full relative h-0">
      {show && (
        <ResponsiveGridLayout
          // onLayoutChange={}
          ref={layoutRef}
          layouts={{
            lg: layout,
            md: layout,
            sm: layout,
            xs: layout,
            xxs: layout,
          }}
          breakpoints={{ lg: 1200, md: 996, sm: 768, xs: 480, xxs: 300 }}
          width={width}
          cols={{ lg: 18, md: 12, sm: 12, xs: 8, xxs: 6 }}
          className="!h-0 pt-[10px]"
          draggableCancel=".no-drag"
          allowOverlap
          autoSize
          rowHeight={1}
          margin={[10, 0]}
        >
          <div
            className="overflow-scroll flex flex-col bg-neutral-900 "
            key="a"
          >
            <div className="w-full h-5 bg-neutral-800 cursor-move"></div>
            <div className="flex-1 no-drag p-2.5">
              <div className="hidden gap-2.5 mb-2.5">
                <Button variant={"secondary"} onClick={() => startTicking()}>
                  start proxy
                </Button>
                <Button variant={"secondary"} onClick={killTicking}>
                  stop proxy
                </Button>
              </div>

              <Chart
                staticPrices={staticTicks}
                handleData={[]}
                brokerData={{
                  currentSymbol: symbol,
                  name: accountName,
                  symbols,
                }}
                ref={chartRef}
                onSymbolChange={onSymbolChange}
              />
            </div>
          </div>
        </ResponsiveGridLayout>
      )}
    </section>
  );
};
export default TradeBench;
