import { TradingChartRef } from "@/widgets/trade-panel/chart/chart";
import { useRef } from "react";

interface Props {
  close?: VoidFunction;
  root?: HTMLElement;
}

const TradePanel: React.FC<Props> = ({}) => {
  const ref = useRef<TradingChartRef>(null);
  // const [staticTicks, setStaticTicks] = useState<any[]>([]);
  // const [symbolHourOffset, setSymbolHourOffset] = useState(0);

  // const symbol = "XRPUSD";

  // symbolHourOffset;

  // const fn = async () => {
  //   const symbol = "XRPUSD";
  //   const data = await requestSession();
  //   console.log(data);

  //   const account = await requestAccount();
  //   console.log(account);

  //   const res = await manageSymbol(symbol, "select");
  //   console.log(res);

  //   const t = tickStart(symbol);
  //   console.log(t);
  // };

  // useEffect(() => {
  //   if (isLoggedIn) {
  //     addTickListener(onTick);
  //     getStaticTicks();
  //   }
  // }, [isLoggedIn]);

  /* useEffect(() => {
    const stat = mockTicks.map(({ ask, bid, time }) => {
      return {
        price: (ask + bid) / 2,
        time,
      };
    });
    setStaticTicks(stat);
  }, []); */

  // const getStaticTicks = async () => {
  //   const data = await manageSymbol(symbol, "select");

  //   const symbolTime = (data?.details?.time * 1000) as number;
  //   const offset = getHourOffset(new Date(symbolTime), new Date());
  //   setSymbolHourOffset(offset);

  //   const now = new Date();
  //   now.setHours(now.getHours() + offset);
  //   const startDate = new Date(now.getTime() - 1000 * 60 * 3);

  //   const staticTicks = await getTicksRange(
  //     symbol,
  //     toLocalISOFormat(now),
  //     toLocalISOFormat(startDate)
  //   );

  //   if (staticTicks?.ticks?.length > 0) {
  //     setStaticTicks(
  //       (staticTicks.ticks as { time: number; bid: number; ask: number }[]).map(
  //         ({ bid, time }) => {
  //           return {
  //             price: bid,
  //             time,
  //           };
  //         }
  //       )
  //     );
  //   }

  //   await delay(1000);
  //   tickStart(symbol);
  // };

  /* 
    useEffect(() => {
      // return;
      const id = simulateTicking();
      return () => clearInterval(id);
    }, []); 
  */

  const simulateTicking = () => {
    let currentTime = new Date().getTime();

    const interval = setInterval(() => {
      const randomIndex = Math.floor(Math.random() * mockTicks.length) - 1;
      const tick = mockTicks[randomIndex] ?? mockTicks[0];

      currentTime + 1000;
      ref.current?.onTick({
        price: (tick.ask + tick.bid) / 2,
        time: new Date().getTime() / 1000,
      });
    }, 1000);
    return interval;
  };
  simulateTicking;

  // const onTick = (data: { time: number; bid: number; ask: number }) => {
  //   // console.log(data,staticTicks.length);
  //   // return;
  //   ref.current?.onTick({
  //     price: toFixed(data.bid, 6),
  //     time: data.time,
  //   });
  // };

  return (
    <div className="w-full flex flex-col p-[5px] relative justify-between">
      
    </div>
  );
};

export const mockTicks = [
  { time: 1734990615, bid: 2.17549, ask: 2.17752 },
  { time: 1734990615, bid: 2.17539, ask: 2.1774 },
  { time: 1734990615, bid: 2.17539, ask: 2.1775 },
  { time: 1734990615, bid: 2.1753, ask: 2.1774 },
  { time: 1734990615, bid: 2.17539, ask: 2.1774 },
  { time: 1734990615, bid: 2.1752, ask: 2.1773 },
  { time: 1734990615, bid: 2.1752, ask: 2.1773 },
  { time: 1734990615, bid: 2.1752, ask: 2.1773 },
  { time: 1734990615, bid: 2.1752, ask: 2.1773 },
  { time: 1734990615, bid: 2.1752, ask: 2.1773 },
  { time: 1734990616, bid: 2.17549, ask: 2.17758 },
  { time: 1734990616, bid: 2.17559, ask: 2.1776 },
  { time: 1734990616, bid: 2.17559, ask: 2.1776 },
  { time: 1734990616, bid: 2.17559, ask: 2.17778 },
  { time: 1734990616, bid: 2.17569, ask: 2.1778 },
  { time: 1734990616, bid: 2.17589, ask: 2.1779 },
  { time: 1734990616, bid: 2.17589, ask: 2.1779 },
  { time: 1734990616, bid: 2.17589, ask: 2.1779 },
  { time: 1734990616, bid: 2.17589, ask: 2.1779 },
  { time: 1734990616, bid: 2.17589, ask: 2.1779 },
  { time: 1734990617, bid: 2.1756, ask: 2.1777 },
  { time: 1734990617, bid: 2.1755, ask: 2.1776 },
  { time: 1734990617, bid: 2.17539, ask: 2.1774 },
  { time: 1734990617, bid: 2.17539, ask: 2.1774 },
  { time: 1734990617, bid: 2.17539, ask: 2.1774 },
  { time: 1734990617, bid: 2.17539, ask: 2.1774 },
  { time: 1734990617, bid: 2.1751, ask: 2.1773 },
  { time: 1734990617, bid: 2.1752, ask: 2.1773 },
  { time: 1734990617, bid: 2.17499, ask: 2.177 },
  { time: 1734990618, bid: 2.17499, ask: 2.177 },
  { time: 1734990618, bid: 2.17499, ask: 2.177 },
  { time: 1734990618, bid: 2.17519, ask: 2.1773 },
  { time: 1734990618, bid: 2.17529, ask: 2.1774 },
  { time: 1734990618, bid: 2.17529, ask: 2.1774 },
  { time: 1734990618, bid: 2.17529, ask: 2.1774 },
  { time: 1734990618, bid: 2.17529, ask: 2.1774 },
  { time: 1734990618, bid: 2.17529, ask: 2.1774 },
  { time: 1734990618, bid: 2.17529, ask: 2.1774 },
  { time: 1734990618, bid: 2.17539, ask: 2.1774 },
  { time: 1734990619, bid: 2.17539, ask: 2.1774 },
  { time: 1734990619, bid: 2.17519, ask: 2.1773 },
  { time: 1734990619, bid: 2.17519, ask: 2.1773 },
  { time: 1734990619, bid: 2.17529, ask: 2.1773 },
  { time: 1734990619, bid: 2.17529, ask: 2.1773 },
  { time: 1734990619, bid: 2.17529, ask: 2.1773 },
  { time: 1734990619, bid: 2.17529, ask: 2.1773 },
  { time: 1734990619, bid: 2.17529, ask: 2.1773 },
  { time: 1734990619, bid: 2.17529, ask: 2.1773 },
  { time: 1734990620, bid: 2.17499, ask: 2.1771 },
  { time: 1734990620, bid: 2.17509, ask: 2.1771 },
  { time: 1734990620, bid: 2.17509, ask: 2.17717 },
  { time: 1734990620, bid: 2.17509, ask: 2.1771 },
  { time: 1734990620, bid: 2.17519, ask: 2.1772 },
  { time: 1734990620, bid: 2.17519, ask: 2.17726 },
  { time: 1734990620, bid: 2.17519, ask: 2.17726 },
  { time: 1734990620, bid: 2.17519, ask: 2.1772 },
  { time: 1734990620, bid: 2.17519, ask: 2.1772 },
  { time: 1734990620, bid: 2.17519, ask: 2.1772 },
  { time: 1734990621, bid: 2.17499, ask: 2.177 },
  { time: 1734990621, bid: 2.17499, ask: 2.1771 },
  { time: 1734990621, bid: 2.17509, ask: 2.1771 },
  { time: 1734990621, bid: 2.17509, ask: 2.1771 },
  { time: 1734990621, bid: 2.17519, ask: 2.1772 },
  { time: 1734990621, bid: 2.17519, ask: 2.17727 },
  { time: 1734990621, bid: 2.17519, ask: 2.17727 },
  { time: 1734990621, bid: 2.17519, ask: 2.17726 },
  { time: 1734990621, bid: 2.17519, ask: 2.17727 },
  { time: 1734990621, bid: 2.17519, ask: 2.17727 },
  { time: 1734990622, bid: 2.17519, ask: 2.17727 },
  { time: 1734990622, bid: 2.17519, ask: 2.1772 },
  { time: 1734990622, bid: 2.17499, ask: 2.1771 },
  { time: 1734990622, bid: 2.17499, ask: 2.1771 },
  { time: 1734990622, bid: 2.17499, ask: 2.1771 },
  { time: 1734990622, bid: 2.17499, ask: 2.1771 },
  { time: 1734990622, bid: 2.17509, ask: 2.1771 },
  { time: 1734990622, bid: 2.17509, ask: 2.1771 },
  { time: 1734990622, bid: 2.17509, ask: 2.1771 },
  { time: 1734990623, bid: 2.17509, ask: 2.1771 },
  { time: 1734990623, bid: 2.17509, ask: 2.1771 },
  { time: 1734990623, bid: 2.17499, ask: 2.1771 },
  { time: 1734990623, bid: 2.17499, ask: 2.1771 },
  { time: 1734990623, bid: 2.17499, ask: 2.177 },
  { time: 1734990623, bid: 2.1746, ask: 2.1768 },
  { time: 1734990623, bid: 2.1746, ask: 2.1768 },
  { time: 1734990623, bid: 2.1746, ask: 2.1768 },
  { time: 1734990623, bid: 2.17479, ask: 2.1768 },
  { time: 1734990623, bid: 2.17479, ask: 2.1768 },
  { time: 1734990624, bid: 2.17479, ask: 2.1768 },
  { time: 1734990624, bid: 2.17489, ask: 2.1769 },
  { time: 1734990624, bid: 2.17479, ask: 2.1768 },
  { time: 1734990624, bid: 2.17479, ask: 2.1768 },
  { time: 1734990624, bid: 2.17479, ask: 2.1768 },
  { time: 1734990624, bid: 2.17479, ask: 2.1768 },
  { time: 1734990624, bid: 2.17479, ask: 2.17689 },
  { time: 1734990624, bid: 2.17479, ask: 2.1768 },
  { time: 1734990624, bid: 2.17479, ask: 2.1768 },
  { time: 1734990624, bid: 2.17479, ask: 2.1768 },
  { time: 1734990625, bid: 2.17479, ask: 2.1768 },
  { time: 1734990625, bid: 2.17479, ask: 2.1768 },
  { time: 1734990625, bid: 2.17479, ask: 2.1768 },
  { time: 1734990625, bid: 2.17479, ask: 2.1768 },
  { time: 1734990625, bid: 2.17479, ask: 2.17689 },
  { time: 1734990625, bid: 2.17479, ask: 2.17689 },
  { time: 1734990625, bid: 2.17479, ask: 2.17688 },
  { time: 1734990625, bid: 2.17479, ask: 2.17688 },
  { time: 1734990625, bid: 2.17479, ask: 2.17689 },
  { time: 1734990626, bid: 2.17479, ask: 2.17689 },
  { time: 1734990626, bid: 2.17479, ask: 2.17689 },
  { time: 1734990626, bid: 2.17479, ask: 2.1768 },
  { time: 1734990626, bid: 2.17479, ask: 2.1768 },
  { time: 1734990626, bid: 2.17479, ask: 2.1768 },
  { time: 1734990626, bid: 2.17479, ask: 2.1768 },
  { time: 1734990626, bid: 2.17479, ask: 2.1768 },
  { time: 1734990626, bid: 2.17479, ask: 2.1768 },
  { time: 1734990626, bid: 2.17479, ask: 2.1768 },
  { time: 1734990626, bid: 2.17479, ask: 2.1768 },
  { time: 1734990627, bid: 2.17479, ask: 2.1768 },
  { time: 1734990627, bid: 2.17479, ask: 2.1768 },
  { time: 1734990627, bid: 2.17479, ask: 2.1768 },
  { time: 1734990627, bid: 2.17479, ask: 2.1768 },
  { time: 1734990627, bid: 2.17479, ask: 2.1768 },
  { time: 1734990627, bid: 2.17479, ask: 2.1768 },
  { time: 1734990627, bid: 2.17471, ask: 2.1768 },
  { time: 1734990627, bid: 2.17459, ask: 2.1766 },
  { time: 1734990627, bid: 2.17469, ask: 2.17678 },
  { time: 1734990627, bid: 2.17469, ask: 2.17678 },
  { time: 1734990628, bid: 2.17469, ask: 2.17678 },
  { time: 1734990628, bid: 2.17479, ask: 2.1768 },
  { time: 1734990628, bid: 2.17479, ask: 2.1768 },
  { time: 1734990628, bid: 2.17479, ask: 2.1768 },
  { time: 1734990628, bid: 2.17479, ask: 2.1768 },
  { time: 1734990628, bid: 2.17499, ask: 2.177 },
  { time: 1734990628, bid: 2.17499, ask: 2.177 },
  { time: 1734990628, bid: 2.17499, ask: 2.177 },
  { time: 1734990628, bid: 2.17499, ask: 2.177 },
  { time: 1734990629, bid: 2.17483, ask: 2.177 },
  { time: 1734990629, bid: 2.17483, ask: 2.177 },
  { time: 1734990629, bid: 2.17479, ask: 2.1768 },
  { time: 1734990629, bid: 2.17479, ask: 2.1768 },
  { time: 1734990629, bid: 2.17489, ask: 2.1769 },
  { time: 1734990629, bid: 2.17489, ask: 2.1769 },
  { time: 1734990629, bid: 2.17489, ask: 2.1769 },
  { time: 1734990629, bid: 2.17489, ask: 2.1769 },
  { time: 1734990629, bid: 2.17489, ask: 2.1769 },
  { time: 1734990629, bid: 2.17489, ask: 2.1769 },
  { time: 1734990630, bid: 2.17489, ask: 2.1769 },
  { time: 1734990630, bid: 2.17489, ask: 2.1769 },
  { time: 1734990630, bid: 2.17489, ask: 2.1769 },
  { time: 1734990630, bid: 2.17489, ask: 2.1769 },
  { time: 1734990630, bid: 2.17489, ask: 2.1769 },
  { time: 1734990630, bid: 2.17489, ask: 2.1769 },
  { time: 1734990630, bid: 2.17489, ask: 2.1769 },
  { time: 1734990630, bid: 2.17489, ask: 2.1769 },
  { time: 1734990630, bid: 2.17473, ask: 2.1769 },
  { time: 1734990630, bid: 2.1748, ask: 2.1769 },
  { time: 1734990631, bid: 2.17481, ask: 2.1769 },
  { time: 1734990631, bid: 2.1748, ask: 2.1769 },
  { time: 1734990631, bid: 2.17459, ask: 2.17668 },
  { time: 1734990631, bid: 2.17469, ask: 2.17689 },
  { time: 1734990631, bid: 2.17469, ask: 2.17689 },
  { time: 1734990631, bid: 2.17469, ask: 2.17689 },
  { time: 1734990631, bid: 2.17469, ask: 2.1767 },
  { time: 1734990631, bid: 2.17469, ask: 2.1767 },
  { time: 1734990631, bid: 2.17469, ask: 2.1767 },
  { time: 1734990632, bid: 2.17469, ask: 2.1767 },
  { time: 1734990632, bid: 2.17469, ask: 2.1767 },
  { time: 1734990632, bid: 2.17469, ask: 2.1767 },
  { time: 1734990632, bid: 2.17469, ask: 2.1767 },
  { time: 1734990632, bid: 2.17469, ask: 2.1767 },
  { time: 1734990632, bid: 2.17469, ask: 2.1767 },
  { time: 1734990632, bid: 2.17469, ask: 2.1767 },
  { time: 1734990632, bid: 2.17469, ask: 2.1767 },
  { time: 1734990632, bid: 2.17459, ask: 2.1766 },
  { time: 1734990632, bid: 2.17459, ask: 2.1766 },
  { time: 1734990633, bid: 2.17459, ask: 2.1766 },
  { time: 1734990633, bid: 2.17459, ask: 2.1766 },
  { time: 1734990633, bid: 2.17459, ask: 2.1766 },
  { time: 1734990633, bid: 2.17469, ask: 2.1767 },
  { time: 1734990633, bid: 2.17469, ask: 2.1767 },
  { time: 1734990633, bid: 2.17469, ask: 2.1767 },
  { time: 1734990633, bid: 2.17469, ask: 2.1767 },
  { time: 1734990633, bid: 2.17469, ask: 2.1767 },
  { time: 1734990633, bid: 2.17469, ask: 2.1767 },
  { time: 1734990634, bid: 2.17469, ask: 2.1767 },
  { time: 1734990634, bid: 2.17469, ask: 2.1767 },
  { time: 1734990634, bid: 2.17469, ask: 2.1767 },
  { time: 1734990634, bid: 2.17469, ask: 2.1767 },
  { time: 1734990634, bid: 2.17469, ask: 2.1767 },
  { time: 1734990634, bid: 2.17469, ask: 2.1767 },
  { time: 1734990634, bid: 2.17469, ask: 2.1767 },
  { time: 1734990634, bid: 2.17469, ask: 2.1767 },
  { time: 1734990634, bid: 2.17469, ask: 2.1767 },
  { time: 1734990634, bid: 2.17469, ask: 2.1767 },
  { time: 1734990635, bid: 2.17469, ask: 2.1767 },
  { time: 1734990635, bid: 2.17469, ask: 2.1767 },
  { time: 1734990635, bid: 2.17469, ask: 2.1767 },
  { time: 1734990635, bid: 2.17469, ask: 2.1767 },
  { time: 1734990635, bid: 2.17469, ask: 2.1767 },
  { time: 1734990635, bid: 2.17469, ask: 2.1767 },
  { time: 1734990635, bid: 2.17469, ask: 2.1767 },
  { time: 1734990635, bid: 2.17469, ask: 2.1767 },
  { time: 1734990635, bid: 2.17469, ask: 2.1767 },
  { time: 1734990635, bid: 2.17469, ask: 2.1767 },
  { time: 1734990636, bid: 2.17479, ask: 2.1768 },
  { time: 1734990636, bid: 2.17479, ask: 2.1768 },
  { time: 1734990636, bid: 2.17479, ask: 2.1768 },
  { time: 1734990636, bid: 2.17479, ask: 2.1768 },
  { time: 1734990636, bid: 2.17479, ask: 2.1768 },
  { time: 1734990636, bid: 2.17489, ask: 2.17697 },
  { time: 1734990636, bid: 2.17489, ask: 2.17707 },
  { time: 1734990636, bid: 2.17519, ask: 2.17736 },
  { time: 1734990636, bid: 2.17559, ask: 2.17769 },
  { time: 1734990637, bid: 2.17559, ask: 2.1776 },
  { time: 1734990637, bid: 2.17529, ask: 2.1774 },
  { time: 1734990637, bid: 2.17499, ask: 2.1771 },
  { time: 1734990637, bid: 2.17499, ask: 2.1771 },
  { time: 1734990637, bid: 2.17509, ask: 2.1771 },
  { time: 1734990637, bid: 2.17509, ask: 2.1771 },
  { time: 1734990637, bid: 2.17509, ask: 2.1771 },
  { time: 1734990637, bid: 2.1748, ask: 2.177 },
  { time: 1734990637, bid: 2.17481, ask: 2.1769 },
  { time: 1734990637, bid: 2.17481, ask: 2.1769 },
  { time: 1734990638, bid: 2.17481, ask: 2.1769 },
  { time: 1734990638, bid: 2.17481, ask: 2.1769 },
  { time: 1734990638, bid: 2.17481, ask: 2.1769 },
  { time: 1734990638, bid: 2.17481, ask: 2.1769 },
  { time: 1734990638, bid: 2.17481, ask: 2.1769 },
  { time: 1734990638, bid: 2.17481, ask: 2.1769 },
  { time: 1734990638, bid: 2.17481, ask: 2.1769 },
  { time: 1734990638, bid: 2.17481, ask: 2.1769 },
  { time: 1734990638, bid: 2.17481, ask: 2.1769 },
  { time: 1734990638, bid: 2.17479, ask: 2.1768 },
  { time: 1734990639, bid: 2.17479, ask: 2.1768 },
  { time: 1734990639, bid: 2.17479, ask: 2.1768 },
  { time: 1734990639, bid: 2.17479, ask: 2.17698 },
  { time: 1734990639, bid: 2.17479, ask: 2.17689 },
  { time: 1734990639, bid: 2.17479, ask: 2.17689 },
  { time: 1734990639, bid: 2.17479, ask: 2.1769 },
  { time: 1734990639, bid: 2.17479, ask: 2.1769 },
  { time: 1734990639, bid: 2.17479, ask: 2.17689 },
  { time: 1734990639, bid: 2.17479, ask: 2.17689 },
  { time: 1734990640, bid: 2.17479, ask: 2.17689 },
  { time: 1734990640, bid: 2.17479, ask: 2.17689 },
  { time: 1734990640, bid: 2.17479, ask: 2.17689 },
  { time: 1734990640, bid: 2.17479, ask: 2.17689 },
  { time: 1734990640, bid: 2.17489, ask: 2.1769 },
  { time: 1734990640, bid: 2.17489, ask: 2.1769 },
  { time: 1734990640, bid: 2.17489, ask: 2.1769 },
  { time: 1734990640, bid: 2.17499, ask: 2.1771 },
  { time: 1734990640, bid: 2.17509, ask: 2.1771 },
  { time: 1734990641, bid: 2.17489, ask: 2.1769 },
  { time: 1734990641, bid: 2.17499, ask: 2.1771 },
  { time: 1734990641, bid: 2.17519, ask: 2.1772 },
  { time: 1734990641, bid: 2.17531, ask: 2.1774 },
  { time: 1734990641, bid: 2.1752, ask: 2.1774 },
  { time: 1734990641, bid: 2.17523, ask: 2.1774 },
  { time: 1734990641, bid: 2.17538, ask: 2.1774 },
  { time: 1734990641, bid: 2.17538, ask: 2.1774 },
  { time: 1734990641, bid: 2.17539, ask: 2.1774 },
  { time: 1734990641, bid: 2.17539, ask: 2.1774 },
  { time: 1734990642, bid: 2.17539, ask: 2.1774 },
  { time: 1734990642, bid: 2.17539, ask: 2.1774 },
  { time: 1734990642, bid: 2.17539, ask: 2.1774 },
  { time: 1734990642, bid: 2.17539, ask: 2.1774 },
  { time: 1734990642, bid: 2.17539, ask: 2.1774 },
  { time: 1734990642, bid: 2.17539, ask: 2.1774 },
  { time: 1734990642, bid: 2.17539, ask: 2.1774 },
  { time: 1734990642, bid: 2.17519, ask: 2.17728 },
  { time: 1734990642, bid: 2.17519, ask: 2.17728 },
  { time: 1734990642, bid: 2.17519, ask: 2.17728 },
  { time: 1734990643, bid: 2.17519, ask: 2.17728 },
  { time: 1734990643, bid: 2.17519, ask: 2.17728 },
  { time: 1734990643, bid: 2.17519, ask: 2.17729 },
  { time: 1734990643, bid: 2.17519, ask: 2.17729 },
  { time: 1734990643, bid: 2.17519, ask: 2.17729 },
  { time: 1734990643, bid: 2.17519, ask: 2.17729 },
  { time: 1734990643, bid: 2.17519, ask: 2.17726 },
  { time: 1734990643, bid: 2.17519, ask: 2.17729 },
  { time: 1734990643, bid: 2.17519, ask: 2.17746 },
  { time: 1734990644, bid: 2.17519, ask: 2.17746 },
  { time: 1734990644, bid: 2.17519, ask: 2.17746 },
  { time: 1734990644, bid: 2.17519, ask: 2.17746 },
  { time: 1734990644, bid: 2.17549, ask: 2.1777 },
  { time: 1734990644, bid: 2.17549, ask: 2.1775 },
  { time: 1734990644, bid: 2.17549, ask: 2.1775 },
  { time: 1734990644, bid: 2.17549, ask: 2.1775 },
  { time: 1734990644, bid: 2.17549, ask: 2.1775 },
  { time: 1734990644, bid: 2.17549, ask: 2.1775 },
  { time: 1734990644, bid: 2.17549, ask: 2.1775 },
  { time: 1734990644, bid: 2.17549, ask: 2.1775 },
  { time: 1734990645, bid: 2.17549, ask: 2.1775 },
  { time: 1734990645, bid: 2.17549, ask: 2.1775 },
  { time: 1734990645, bid: 2.17549, ask: 2.17767 },
  { time: 1734990645, bid: 2.17549, ask: 2.17767 },
  { time: 1734990645, bid: 2.17549, ask: 2.17754 },
  { time: 1734990645, bid: 2.17549, ask: 2.17768 },
  { time: 1734990645, bid: 2.17549, ask: 2.17768 },
  { time: 1734990645, bid: 2.17549, ask: 2.17768 },
  { time: 1734990645, bid: 2.17549, ask: 2.17754 },
  { time: 1734990646, bid: 2.17549, ask: 2.17766 },
  { time: 1734990646, bid: 2.17549, ask: 2.17765 },
  { time: 1734990646, bid: 2.17559, ask: 2.17769 },
  { time: 1734990646, bid: 2.17559, ask: 2.17769 },
  { time: 1734990646, bid: 2.17559, ask: 2.17769 },
  { time: 1734990646, bid: 2.17559, ask: 2.17769 },
  { time: 1734990646, bid: 2.17559, ask: 2.1776 },
  { time: 1734990646, bid: 2.17531, ask: 2.1774 },
  { time: 1734990646, bid: 2.17539, ask: 2.1774 },
  { time: 1734990647, bid: 2.17539, ask: 2.1774 },
  { time: 1734990647, bid: 2.17539, ask: 2.1774 },
  { time: 1734990647, bid: 2.17539, ask: 2.17749 },
  { time: 1734990647, bid: 2.17539, ask: 2.1774 },
  { time: 1734990647, bid: 2.17539, ask: 2.1774 },
  { time: 1734990647, bid: 2.17539, ask: 2.1774 },
  { time: 1734990647, bid: 2.17539, ask: 2.1774 },
  { time: 1734990647, bid: 2.17539, ask: 2.1774 },
  { time: 1734990647, bid: 2.17539, ask: 2.1774 },
  { time: 1734990647, bid: 2.17539, ask: 2.1774 },
  { time: 1734990648, bid: 2.17539, ask: 2.1774 },
  { time: 1734990648, bid: 2.17539, ask: 2.1774 },
  { time: 1734990648, bid: 2.17519, ask: 2.1772 },
  { time: 1734990648, bid: 2.17519, ask: 2.1772 },
  { time: 1734990648, bid: 2.17519, ask: 2.1772 },
  { time: 1734990648, bid: 2.17519, ask: 2.1772 },
  { time: 1734990648, bid: 2.17519, ask: 2.1772 },
  { time: 1734990648, bid: 2.17529, ask: 2.1773 },
  { time: 1734990648, bid: 2.17522, ask: 2.1773 },
  { time: 1734990648, bid: 2.17529, ask: 2.1773 },
  { time: 1734990649, bid: 2.17529, ask: 2.17739 },
  { time: 1734990649, bid: 2.17529, ask: 2.17739 },
  { time: 1734990649, bid: 2.17529, ask: 2.17749 },
  { time: 1734990649, bid: 2.17529, ask: 2.17749 },
  { time: 1734990649, bid: 2.17529, ask: 2.17747 },
  { time: 1734990649, bid: 2.17529, ask: 2.17748 },
  { time: 1734990649, bid: 2.17539, ask: 2.17769 },
  { time: 1734990649, bid: 2.17551, ask: 2.1777 },
  { time: 1734990649, bid: 2.17569, ask: 2.17789 },
  { time: 1734990650, bid: 2.17569, ask: 2.17789 },
  { time: 1734990650, bid: 2.17569, ask: 2.1777 },
  { time: 1734990650, bid: 2.17569, ask: 2.17787 },
  { time: 1734990650, bid: 2.17569, ask: 2.17787 },
  { time: 1734990650, bid: 2.17569, ask: 2.17787 },
  { time: 1734990650, bid: 2.17569, ask: 2.1778399999999998 },
  { time: 1734990650, bid: 2.17569, ask: 2.1777 },
  { time: 1734990650, bid: 2.17569, ask: 2.1777 },
  { time: 1734990650, bid: 2.17569, ask: 2.17783 },
  { time: 1734990650, bid: 2.17569, ask: 2.17783 },
  { time: 1734990651, bid: 2.17569, ask: 2.17785 },
  { time: 1734990651, bid: 2.17569, ask: 2.17785 },
  { time: 1734990651, bid: 2.17569, ask: 2.17785 },
  { time: 1734990651, bid: 2.17569, ask: 2.17785 },
  { time: 1734990651, bid: 2.17569, ask: 2.17785 },
  { time: 1734990651, bid: 2.17569, ask: 2.17785 },
  { time: 1734990651, bid: 2.17599, ask: 2.178 },
  { time: 1734990651, bid: 2.17599, ask: 2.178 },
  { time: 1734990651, bid: 2.17599, ask: 2.178 },
  { time: 1734990651, bid: 2.17599, ask: 2.178 },
  { time: 1734990652, bid: 2.17561, ask: 2.1777 },
  { time: 1734990652, bid: 2.17562, ask: 2.1777 },
  { time: 1734990652, bid: 2.1756, ask: 2.1777 },
  { time: 1734990652, bid: 2.17562, ask: 2.1777 },
  { time: 1734990652, bid: 2.1756, ask: 2.1777 },
  { time: 1734990652, bid: 2.17561, ask: 2.1777 },
  { time: 1734990652, bid: 2.17561, ask: 2.1777 },
  { time: 1734990652, bid: 2.17569, ask: 2.1777 },
  { time: 1734990652, bid: 2.17569, ask: 2.1777 },
  { time: 1734990653, bid: 2.17569, ask: 2.1777 },
  { time: 1734990653, bid: 2.17519, ask: 2.1773 },
  { time: 1734990653, bid: 2.17519, ask: 2.1772 },
  { time: 1734990653, bid: 2.17529, ask: 2.1773 },
  { time: 1734990653, bid: 2.17549, ask: 2.1775 },
  { time: 1734990653, bid: 2.17559, ask: 2.17769 },
  { time: 1734990653, bid: 2.1755, ask: 2.1776 },
  { time: 1734990653, bid: 2.17549, ask: 2.1775 },
  { time: 1734990653, bid: 2.17569, ask: 2.17788 },
  { time: 1734990653, bid: 2.17569, ask: 2.17788 },
  { time: 1734990654, bid: 2.17569, ask: 2.17773 },
  { time: 1734990654, bid: 2.17569, ask: 2.1777 },
  { time: 1734990654, bid: 2.17569, ask: 2.1777 },
  { time: 1734990654, bid: 2.17569, ask: 2.1777 },
  { time: 1734990654, bid: 2.17569, ask: 2.1777 },
  { time: 1734990654, bid: 2.17569, ask: 2.1777 },
  { time: 1734990654, bid: 2.17569, ask: 2.1777 },
  { time: 1734990654, bid: 2.17569, ask: 2.1777 },
  { time: 1734990654, bid: 2.17579, ask: 2.17788 },
  { time: 1734990655, bid: 2.17579, ask: 2.17789 },
  { time: 1734990655, bid: 2.17579, ask: 2.17789 },
  { time: 1734990655, bid: 2.17579, ask: 2.17789 },
  { time: 1734990655, bid: 2.17579, ask: 2.17789 },
  { time: 1734990655, bid: 2.17579, ask: 2.17789 },
  { time: 1734990655, bid: 2.17579, ask: 2.1778 },
  { time: 1734990655, bid: 2.17579, ask: 2.1778 },
  { time: 1734990655, bid: 2.17579, ask: 2.17788 },
  { time: 1734990655, bid: 2.17579, ask: 2.17786 },
  { time: 1734990655, bid: 2.17579, ask: 2.17786 },
  { time: 1734990656, bid: 2.17579, ask: 2.17786 },
  { time: 1734990656, bid: 2.17579, ask: 2.17785 },
  { time: 1734990656, bid: 2.17579, ask: 2.1778 },
  { time: 1734990656, bid: 2.17579, ask: 2.1778 },
  { time: 1734990656, bid: 2.17579, ask: 2.1778 },
  { time: 1734990656, bid: 2.17579, ask: 2.1778 },
  { time: 1734990656, bid: 2.17579, ask: 2.1778 },
  { time: 1734990656, bid: 2.17579, ask: 2.1778 },
  { time: 1734990656, bid: 2.17579, ask: 2.1778 },
  { time: 1734990656, bid: 2.17579, ask: 2.1778 },
  { time: 1734990657, bid: 2.17579, ask: 2.1778 },
  { time: 1734990657, bid: 2.17579, ask: 2.1778 },
  { time: 1734990657, bid: 2.17579, ask: 2.1778 },
  { time: 1734990657, bid: 2.17579, ask: 2.1778 },
  { time: 1734990657, bid: 2.17563, ask: 2.1777 },
  { time: 1734990657, bid: 2.17569, ask: 2.1777 },
  { time: 1734990657, bid: 2.17544, ask: 2.1775 },
  { time: 1734990657, bid: 2.17541, ask: 2.1775 },
  { time: 1734990657, bid: 2.1754, ask: 2.1775 },
  { time: 1734990657, bid: 2.17541, ask: 2.1775 },
  { time: 1734990658, bid: 2.17541, ask: 2.1775 },
  { time: 1734990658, bid: 2.17541, ask: 2.1775 },
  { time: 1734990658, bid: 2.17541, ask: 2.1775 },
  { time: 1734990658, bid: 2.17541, ask: 2.1775 },
  { time: 1734990658, bid: 2.17541, ask: 2.1775 },
  { time: 1734990658, bid: 2.17549, ask: 2.1775 },
  { time: 1734990658, bid: 2.17549, ask: 2.1775 },
  { time: 1734990658, bid: 2.17559, ask: 2.1776 },
  { time: 1734990658, bid: 2.17501, ask: 2.1771 },
  { time: 1734990659, bid: 2.17498, ask: 2.17718 },
  { time: 1734990659, bid: 2.17509, ask: 2.17715 },
  { time: 1734990659, bid: 2.17519, ask: 2.1773 },
  { time: 1734990659, bid: 2.17519, ask: 2.17729 },
  { time: 1734990659, bid: 2.17508, ask: 2.1772 },
  { time: 1734990659, bid: 2.17519, ask: 2.17729 },
  { time: 1734990659, bid: 2.17519, ask: 2.17728 },
  { time: 1734990659, bid: 2.17519, ask: 2.17728 },
  { time: 1734990659, bid: 2.17519, ask: 2.17728 },
  { time: 1734990659, bid: 2.17519, ask: 2.17728 },
  { time: 1734990660, bid: 2.17519, ask: 2.17729 },
  { time: 1734990660, bid: 2.17549, ask: 2.1775 },
  { time: 1734990660, bid: 2.17569, ask: 2.1777 },
  { time: 1734990660, bid: 2.17579, ask: 2.1778 },
  { time: 1734990660, bid: 2.17579, ask: 2.1778 },
  { time: 1734990660, bid: 2.17579, ask: 2.1778 },
  { time: 1734990660, bid: 2.17579, ask: 2.1778 },
  { time: 1734990660, bid: 2.17579, ask: 2.1778 },
  { time: 1734990660, bid: 2.17556, ask: 2.1777 },
  { time: 1734990661, bid: 2.17541, ask: 2.17769 },
  { time: 1734990661, bid: 2.17564, ask: 2.1777 },
  { time: 1734990661, bid: 2.17564, ask: 2.1777 },
  { time: 1734990661, bid: 2.17569, ask: 2.1777 },
  { time: 1734990661, bid: 2.17569, ask: 2.1777 },
  { time: 1734990661, bid: 2.17579, ask: 2.1778 },
  { time: 1734990661, bid: 2.17579, ask: 2.1778 },
  { time: 1734990661, bid: 2.17599, ask: 2.178 },
  { time: 1734990661, bid: 2.17609, ask: 2.1781 },
  { time: 1734990662, bid: 2.1760099999999998, ask: 2.1781 },
  { time: 1734990662, bid: 2.17602, ask: 2.1781 },
  { time: 1734990662, bid: 2.17602, ask: 2.1781 },
  { time: 1734990662, bid: 2.17541, ask: 2.1776 },
  { time: 1734990662, bid: 2.17559, ask: 2.1776 },
  { time: 1734990662, bid: 2.17559, ask: 2.1776 },
  { time: 1734990662, bid: 2.17559, ask: 2.1776 },
  { time: 1734990662, bid: 2.17559, ask: 2.1776 },
  { time: 1734990662, bid: 2.17559, ask: 2.1776 },
  { time: 1734990662, bid: 2.17559, ask: 2.1776 },
  { time: 1734990662, bid: 2.17559, ask: 2.1776 },
  { time: 1734990663, bid: 2.17559, ask: 2.17777 },
  { time: 1734990663, bid: 2.17559, ask: 2.17777 },
  { time: 1734990663, bid: 2.17579, ask: 2.17789 },
  { time: 1734990663, bid: 2.17579, ask: 2.17789 },
  { time: 1734990663, bid: 2.17579, ask: 2.17789 },
  { time: 1734990663, bid: 2.17579, ask: 2.17789 },
  { time: 1734990663, bid: 2.17579, ask: 2.17789 },
  { time: 1734990663, bid: 2.17579, ask: 2.17789 },
  { time: 1734990663, bid: 2.17579, ask: 2.17789 },
  { time: 1734990664, bid: 2.17579, ask: 2.17789 },
  { time: 1734990664, bid: 2.17579, ask: 2.17789 },
  { time: 1734990664, bid: 2.17571, ask: 2.1778 },
  { time: 1734990664, bid: 2.17579, ask: 2.1778 },
  { time: 1734990664, bid: 2.17579, ask: 2.17789 },
  { time: 1734990664, bid: 2.17579, ask: 2.17788 },
  { time: 1734990664, bid: 2.17609, ask: 2.1781 },
  { time: 1734990664, bid: 2.17609, ask: 2.1781 },
  { time: 1734990664, bid: 2.176, ask: 2.1781 },
  { time: 1734990665, bid: 2.176, ask: 2.1781 },
  { time: 1734990665, bid: 2.176, ask: 2.1781 },
  { time: 1734990665, bid: 2.176, ask: 2.1781 },
  { time: 1734990665, bid: 2.176, ask: 2.1781 },
  { time: 1734990665, bid: 2.176, ask: 2.1781 },
  { time: 1734990665, bid: 2.176, ask: 2.1781 },
  { time: 1734990665, bid: 2.176, ask: 2.1781 },
  { time: 1734990665, bid: 2.17599, ask: 2.178 },
  { time: 1734990665, bid: 2.17609, ask: 2.1781 },
  { time: 1734990665, bid: 2.17609, ask: 2.1781 },
  { time: 1734990666, bid: 2.17609, ask: 2.1781 },
  { time: 1734990666, bid: 2.17609, ask: 2.1781 },
  { time: 1734990666, bid: 2.17609, ask: 2.1781 },
  { time: 1734990666, bid: 2.17609, ask: 2.17819 },
  { time: 1734990666, bid: 2.17609, ask: 2.17819 },
  { time: 1734990666, bid: 2.17609, ask: 2.17819 },
  { time: 1734990666, bid: 2.1760099999999998, ask: 2.1781 },
  { time: 1734990666, bid: 2.176, ask: 2.1781 },
  { time: 1734990666, bid: 2.17609, ask: 2.17819 },
  { time: 1734990666, bid: 2.17609, ask: 2.17819 },
  { time: 1734990667, bid: 2.17619, ask: 2.1782 },
  { time: 1734990667, bid: 2.17619, ask: 2.1782 },
  { time: 1734990667, bid: 2.17619, ask: 2.1782 },
  { time: 1734990667, bid: 2.17619, ask: 2.1782 },
  { time: 1734990667, bid: 2.17619, ask: 2.1782 },
  { time: 1734990667, bid: 2.17619, ask: 2.1782 },
  { time: 1734990667, bid: 2.17619, ask: 2.1782 },
  { time: 1734990667, bid: 2.17619, ask: 2.1782 },
  { time: 1734990667, bid: 2.17639, ask: 2.17858 },
  { time: 1734990668, bid: 2.17639, ask: 2.17858 },
  { time: 1734990668, bid: 2.17639, ask: 2.1784 },
  { time: 1734990668, bid: 2.17629, ask: 2.1783 },
  { time: 1734990668, bid: 2.17629, ask: 2.1783 },
  { time: 1734990668, bid: 2.17629, ask: 2.17833 },
  { time: 1734990668, bid: 2.17629, ask: 2.17833 },
  { time: 1734990668, bid: 2.17629, ask: 2.17833 },
  { time: 1734990668, bid: 2.17639, ask: 2.17849 },
  { time: 1734990668, bid: 2.17639, ask: 2.17856 },
  { time: 1734990668, bid: 2.17699, ask: 2.179 },
  { time: 1734990669, bid: 2.17699, ask: 2.17909 },
  { time: 1734990669, bid: 2.17699, ask: 2.179 },
  { time: 1734990669, bid: 2.17691, ask: 2.179 },
  { time: 1734990669, bid: 2.17699, ask: 2.1791 },
  { time: 1734990669, bid: 2.17699, ask: 2.1791 },
  { time: 1734990669, bid: 2.17699, ask: 2.17908 },
  { time: 1734990669, bid: 2.17729, ask: 2.1793 },
  { time: 1734990669, bid: 2.17729, ask: 2.1793 },
  { time: 1734990669, bid: 2.17739, ask: 2.1795 },
  { time: 1734990669, bid: 2.17749, ask: 2.1795 },
  { time: 1734990670, bid: 2.17749, ask: 2.1795 },
  { time: 1734990670, bid: 2.17749, ask: 2.1796 },
  { time: 1734990670, bid: 2.17759, ask: 2.1796 },
  { time: 1734990670, bid: 2.17759, ask: 2.1796 },
  { time: 1734990670, bid: 2.17759, ask: 2.1796 },
  { time: 1734990670, bid: 2.17759, ask: 2.1796 },
  { time: 1734990670, bid: 2.17769, ask: 2.17989 },
  { time: 1734990670, bid: 2.17769, ask: 2.1797 },
  { time: 1734990670, bid: 2.17769, ask: 2.1797 },
  { time: 1734990670, bid: 2.17769, ask: 2.1797 },
  { time: 1734990671, bid: 2.17769, ask: 2.1797 },
  { time: 1734990671, bid: 2.17769, ask: 2.1797 },
  { time: 1734990671, bid: 2.17769, ask: 2.1797 },
  { time: 1734990671, bid: 2.17769, ask: 2.1797 },
  { time: 1734990671, bid: 2.17769, ask: 2.1797 },
  { time: 1734990671, bid: 2.17769, ask: 2.17988 },
  { time: 1734990671, bid: 2.17769, ask: 2.17989 },
  { time: 1734990671, bid: 2.17779, ask: 2.17996 },
  { time: 1734990671, bid: 2.17779, ask: 2.18008 },
  { time: 1734990672, bid: 2.17809, ask: 2.18019 },
  { time: 1734990672, bid: 2.17809, ask: 2.18038 },
  { time: 1734990672, bid: 2.17809, ask: 2.18029 },
  { time: 1734990672, bid: 2.17809, ask: 2.1802799999999998 },
  { time: 1734990672, bid: 2.17809, ask: 2.1803 },
  { time: 1734990672, bid: 2.17829, ask: 2.1803 },
  { time: 1734990672, bid: 2.17839, ask: 2.1806 },
  { time: 1734990672, bid: 2.17849, ask: 2.1805 },
  { time: 1734990672, bid: 2.17849, ask: 2.1805 },
  { time: 1734990672, bid: 2.17819, ask: 2.1802 },
  { time: 1734990673, bid: 2.17809, ask: 2.1802 },
  { time: 1734990673, bid: 2.17811, ask: 2.1802 },
  { time: 1734990673, bid: 2.17811, ask: 2.1802 },
  { time: 1734990673, bid: 2.17811, ask: 2.1802 },
  { time: 1734990673, bid: 2.17819, ask: 2.1802799999999998 },
  { time: 1734990673, bid: 2.17811, ask: 2.1802 },
  { time: 1734990673, bid: 2.17771, ask: 2.1799 },
  { time: 1734990673, bid: 2.17771, ask: 2.1798 },
  { time: 1734990673, bid: 2.1775, ask: 2.1796 },
  { time: 1734990673, bid: 2.17729, ask: 2.1795 },
  { time: 1734990674, bid: 2.17709, ask: 2.1794000000000002 },
  { time: 1734990674, bid: 2.17719, ask: 2.1792 },
  { time: 1734990674, bid: 2.17719, ask: 2.1792 },
  { time: 1734990674, bid: 2.17702, ask: 2.1792 },
  { time: 1734990674, bid: 2.17719, ask: 2.1793 },
  { time: 1734990674, bid: 2.17729, ask: 2.1793 },
  { time: 1734990674, bid: 2.17724, ask: 2.1793 },
  { time: 1734990674, bid: 2.17705, ask: 2.1792 },
  { time: 1734990674, bid: 2.17705, ask: 2.1792 },
  { time: 1734990675, bid: 2.17714, ask: 2.1792 },
  { time: 1734990675, bid: 2.17719, ask: 2.1792 },
  { time: 1734990675, bid: 2.17719, ask: 2.1792 },
  { time: 1734990675, bid: 2.17719, ask: 2.1792 },
  { time: 1734990675, bid: 2.17729, ask: 2.1793 },
  { time: 1734990675, bid: 2.17729, ask: 2.1793 },
  { time: 1734990675, bid: 2.17721, ask: 2.1793 },
  { time: 1734990675, bid: 2.17721, ask: 2.1793 },
  { time: 1734990675, bid: 2.1772, ask: 2.1793 },
  { time: 1734990675, bid: 2.1772, ask: 2.1793 },
  { time: 1734990676, bid: 2.17693, ask: 2.179 },
  { time: 1734990676, bid: 2.17692, ask: 2.179 },
  { time: 1734990676, bid: 2.1765, ask: 2.1787 },
  { time: 1734990676, bid: 2.17679, ask: 2.17889 },
  { time: 1734990676, bid: 2.17699, ask: 2.17908 },
  { time: 1734990676, bid: 2.17694, ask: 2.179 },
  { time: 1734990676, bid: 2.1769, ask: 2.179 },
  { time: 1734990676, bid: 2.1769, ask: 2.179 },
  { time: 1734990676, bid: 2.1769, ask: 2.179 },
  { time: 1734990676, bid: 2.17699, ask: 2.179 },
  { time: 1734990677, bid: 2.17709, ask: 2.1791 },
  { time: 1734990677, bid: 2.17709, ask: 2.1791 },
  { time: 1734990677, bid: 2.17709, ask: 2.1791 },
  { time: 1734990677, bid: 2.17709, ask: 2.1791 },
  { time: 1734990677, bid: 2.17709, ask: 2.1791 },
  { time: 1734990677, bid: 2.17709, ask: 2.1791 },
  { time: 1734990677, bid: 2.17709, ask: 2.1791 },
  { time: 1734990677, bid: 2.17709, ask: 2.1791 },
  { time: 1734990677, bid: 2.1764, ask: 2.1786 },
  { time: 1734990677, bid: 2.17631, ask: 2.1785 },
  { time: 1734990678, bid: 2.17639, ask: 2.1785 },
  { time: 1734990678, bid: 2.17639, ask: 2.1784 },
  { time: 1734990678, bid: 2.17649, ask: 2.17856 },
  { time: 1734990678, bid: 2.17649, ask: 2.17857 },
  { time: 1734990678, bid: 2.17649, ask: 2.17869 },
  { time: 1734990678, bid: 2.17649, ask: 2.17868 },
  { time: 1734990678, bid: 2.17649, ask: 2.17876 },
  { time: 1734990678, bid: 2.17649, ask: 2.17875 },
  { time: 1734990678, bid: 2.17649, ask: 2.17867 },
  { time: 1734990679, bid: 2.17649, ask: 2.17867 },
  { time: 1734990679, bid: 2.17649, ask: 2.17867 },
  { time: 1734990679, bid: 2.17649, ask: 2.17867 },
  { time: 1734990679, bid: 2.17649, ask: 2.17867 },
  { time: 1734990679, bid: 2.17649, ask: 2.17867 },
  { time: 1734990679, bid: 2.17649, ask: 2.17867 },
  { time: 1734990679, bid: 2.17649, ask: 2.17868 },
  { time: 1734990679, bid: 2.17669, ask: 2.17878 },
  { time: 1734990679, bid: 2.17669, ask: 2.17878 },
  { time: 1734990679, bid: 2.17679, ask: 2.17889 },
  { time: 1734990680, bid: 2.17679, ask: 2.1788 },
  { time: 1734990680, bid: 2.17671, ask: 2.1788 },
  { time: 1734990680, bid: 2.17641, ask: 2.1786 },
  { time: 1734990680, bid: 2.17641, ask: 2.1786 },
  { time: 1734990680, bid: 2.17641, ask: 2.1786 },
  { time: 1734990680, bid: 2.17659, ask: 2.1786 },
  { time: 1734990680, bid: 2.17651, ask: 2.1786 },
  { time: 1734990680, bid: 2.17651, ask: 2.1786 },
  { time: 1734990680, bid: 2.17651, ask: 2.1786 },
  { time: 1734990681, bid: 2.1762, ask: 2.1783 },
  { time: 1734990681, bid: 2.17621, ask: 2.1783 },
  { time: 1734990681, bid: 2.17621, ask: 2.1783 },
  { time: 1734990681, bid: 2.17621, ask: 2.1783 },
  { time: 1734990681, bid: 2.17621, ask: 2.1783 },
  { time: 1734990681, bid: 2.1762, ask: 2.1783 },
  { time: 1734990681, bid: 2.17621, ask: 2.1783 },
  { time: 1734990681, bid: 2.1762, ask: 2.1783 },
  { time: 1734990681, bid: 2.17619, ask: 2.1783 },
  { time: 1734990681, bid: 2.17619, ask: 2.1783 },
  { time: 1734990682, bid: 2.17619, ask: 2.1782 },
  { time: 1734990682, bid: 2.17619, ask: 2.1782 },
  { time: 1734990682, bid: 2.17611, ask: 2.1782 },
  { time: 1734990682, bid: 2.17611, ask: 2.1782 },
  { time: 1734990682, bid: 2.17619, ask: 2.1782 },
  { time: 1734990682, bid: 2.17619, ask: 2.1782 },
  { time: 1734990682, bid: 2.17619, ask: 2.1782 },
  { time: 1734990682, bid: 2.17619, ask: 2.1782 },
  { time: 1734990682, bid: 2.17619, ask: 2.1782 },
  { time: 1734990683, bid: 2.17639, ask: 2.17858 },
  { time: 1734990683, bid: 2.17639, ask: 2.1784 },
  { time: 1734990683, bid: 2.17632, ask: 2.1784 },
  { time: 1734990683, bid: 2.17639, ask: 2.1784 },
  { time: 1734990683, bid: 2.17639, ask: 2.17848 },
  { time: 1734990683, bid: 2.17639, ask: 2.17848 },
  { time: 1734990683, bid: 2.17639, ask: 2.17848 },
  { time: 1734990683, bid: 2.17639, ask: 2.17857 },
  { time: 1734990683, bid: 2.17639, ask: 2.17846 },
  { time: 1734990683, bid: 2.17639, ask: 2.17846 },
  { time: 1734990684, bid: 2.17639, ask: 2.17849 },
  { time: 1734990684, bid: 2.17639, ask: 2.1784 },
  { time: 1734990684, bid: 2.17639, ask: 2.1784 },
  { time: 1734990684, bid: 2.17639, ask: 2.1784 },
  { time: 1734990684, bid: 2.17639, ask: 2.1784 },
  { time: 1734990684, bid: 2.17639, ask: 2.1784 },
  { time: 1734990684, bid: 2.17639, ask: 2.1784 },
  { time: 1734990684, bid: 2.17639, ask: 2.1784 },
  { time: 1734990684, bid: 2.17639, ask: 2.17847 },
  { time: 1734990685, bid: 2.17639, ask: 2.17849 },
  { time: 1734990685, bid: 2.17639, ask: 2.1784 },
  { time: 1734990685, bid: 2.17639, ask: 2.1784 },
  { time: 1734990685, bid: 2.17639, ask: 2.17846 },
  { time: 1734990685, bid: 2.17639, ask: 2.17846 },
  { time: 1734990685, bid: 2.17639, ask: 2.17846 },
  { time: 1734990685, bid: 2.17649, ask: 2.17858 },
  { time: 1734990685, bid: 2.17649, ask: 2.17858 },
  { time: 1734990685, bid: 2.17649, ask: 2.17857 },
  { time: 1734990686, bid: 2.17649, ask: 2.17857 },
  { time: 1734990686, bid: 2.17659, ask: 2.1787 },
  { time: 1734990686, bid: 2.17659, ask: 2.17868 },
  { time: 1734990686, bid: 2.17659, ask: 2.1787 },
  { time: 1734990686, bid: 2.17659, ask: 2.1787 },
  { time: 1734990686, bid: 2.17669, ask: 2.1787 },
  { time: 1734990686, bid: 2.17669, ask: 2.1787 },
  { time: 1734990686, bid: 2.17669, ask: 2.1787 },
  { time: 1734990686, bid: 2.17669, ask: 2.1788 },
  { time: 1734990686, bid: 2.17669, ask: 2.17877 },
  { time: 1734990687, bid: 2.17669, ask: 2.17877 },
  { time: 1734990687, bid: 2.17669, ask: 2.1787 },
  { time: 1734990687, bid: 2.17669, ask: 2.1787 },
  { time: 1734990687, bid: 2.17669, ask: 2.1787 },
  { time: 1734990687, bid: 2.17669, ask: 2.1787 },
  { time: 1734990687, bid: 2.17669, ask: 2.1787 },
  { time: 1734990687, bid: 2.17669, ask: 2.1787900000000002 },
  { time: 1734990687, bid: 2.17669, ask: 2.1787900000000002 },
  { time: 1734990687, bid: 2.17669, ask: 2.17876 },
  { time: 1734990687, bid: 2.17669, ask: 2.17876 },
  { time: 1734990688, bid: 2.17669, ask: 2.1787 },
  { time: 1734990688, bid: 2.17669, ask: 2.1787 },
  { time: 1734990688, bid: 2.17669, ask: 2.1787 },
  { time: 1734990688, bid: 2.17669, ask: 2.1787 },
  { time: 1734990688, bid: 2.17669, ask: 2.1787 },
  { time: 1734990688, bid: 2.17669, ask: 2.1787 },
  { time: 1734990688, bid: 2.17669, ask: 2.1787 },
  { time: 1734990688, bid: 2.17669, ask: 2.1787 },
  { time: 1734990688, bid: 2.17669, ask: 2.1787 },
  { time: 1734990689, bid: 2.17669, ask: 2.1787 },
  { time: 1734990689, bid: 2.17669, ask: 2.1787 },
  { time: 1734990689, bid: 2.17669, ask: 2.1787 },
  { time: 1734990689, bid: 2.17669, ask: 2.1787 },
  { time: 1734990689, bid: 2.17669, ask: 2.1787 },
  { time: 1734990689, bid: 2.17669, ask: 2.1787 },
  { time: 1734990689, bid: 2.17669, ask: 2.1787 },
  { time: 1734990689, bid: 2.17669, ask: 2.17888 },
  { time: 1734990689, bid: 2.17669, ask: 2.17876 },
  { time: 1734990690, bid: 2.17669, ask: 2.17876 },
  { time: 1734990690, bid: 2.17669, ask: 2.1787 },
  { time: 1734990690, bid: 2.17669, ask: 2.1787 },
  { time: 1734990690, bid: 2.17669, ask: 2.1787 },
  { time: 1734990690, bid: 2.17669, ask: 2.1787 },
  { time: 1734990690, bid: 2.17669, ask: 2.1787 },
  { time: 1734990690, bid: 2.17669, ask: 2.17876 },
  { time: 1734990690, bid: 2.17669, ask: 2.1787 },
  { time: 1734990690, bid: 2.17669, ask: 2.1787 },
  { time: 1734990690, bid: 2.17669, ask: 2.1787 },
  { time: 1734990691, bid: 2.17669, ask: 2.1787 },
  { time: 1734990691, bid: 2.17669, ask: 2.1787 },
  { time: 1734990691, bid: 2.17669, ask: 2.1787 },
  { time: 1734990691, bid: 2.17669, ask: 2.1787 },
  { time: 1734990691, bid: 2.17669, ask: 2.1787 },
  { time: 1734990691, bid: 2.17669, ask: 2.1787 },
  { time: 1734990691, bid: 2.17669, ask: 2.1787 },
  { time: 1734990691, bid: 2.17669, ask: 2.1787 },
  { time: 1734990691, bid: 2.17669, ask: 2.17876 },
  { time: 1734990692, bid: 2.17669, ask: 2.17876 },
  { time: 1734990692, bid: 2.17669, ask: 2.17876 },
  { time: 1734990692, bid: 2.17669, ask: 2.17876 },
  { time: 1734990692, bid: 2.17669, ask: 2.17875 },
  { time: 1734990692, bid: 2.17669, ask: 2.17875 },
  { time: 1734990692, bid: 2.17669, ask: 2.17876 },
  { time: 1734990692, bid: 2.17669, ask: 2.17873 },
  { time: 1734990692, bid: 2.17669, ask: 2.17887 },
  { time: 1734990692, bid: 2.17669, ask: 2.17875 },
  { time: 1734990692, bid: 2.17669, ask: 2.1787 },
  { time: 1734990693, bid: 2.17669, ask: 2.1787 },
  { time: 1734990693, bid: 2.17669, ask: 2.1787 },
  { time: 1734990693, bid: 2.17669, ask: 2.1787 },
  { time: 1734990693, bid: 2.17669, ask: 2.1787 },
  { time: 1734990693, bid: 2.17669, ask: 2.1787 },
  { time: 1734990693, bid: 2.17669, ask: 2.1787 },
  { time: 1734990693, bid: 2.17669, ask: 2.17888 },
  { time: 1734990693, bid: 2.17679, ask: 2.17897 },
  { time: 1734990693, bid: 2.17679, ask: 2.17908 },
  { time: 1734990693, bid: 2.17699, ask: 2.17909 },
  { time: 1734990694, bid: 2.17699, ask: 2.17918 },
  { time: 1734990694, bid: 2.17709, ask: 2.1791 },
  { time: 1734990694, bid: 2.17709, ask: 2.17928 },
  { time: 1734990694, bid: 2.17709, ask: 2.17929 },
  { time: 1734990694, bid: 2.17709, ask: 2.17928 },
  { time: 1734990694, bid: 2.17709, ask: 2.17928 },
  { time: 1734990694, bid: 2.17709, ask: 2.17928 },
  { time: 1734990694, bid: 2.17709, ask: 2.1791 },
  { time: 1734990694, bid: 2.17709, ask: 2.1791 },
  { time: 1734990695, bid: 2.17709, ask: 2.1791 },
  { time: 1734990695, bid: 2.17709, ask: 2.1791 },
  { time: 1734990695, bid: 2.17709, ask: 2.17913 },
  { time: 1734990695, bid: 2.17709, ask: 2.1791 },
  { time: 1734990695, bid: 2.17709, ask: 2.1791 },
  { time: 1734990695, bid: 2.17709, ask: 2.1791 },
  { time: 1734990695, bid: 2.17709, ask: 2.1791 },
  { time: 1734990695, bid: 2.17709, ask: 2.1791 },
  { time: 1734990695, bid: 2.17709, ask: 2.1791 },
  { time: 1734990695, bid: 2.17709, ask: 2.17917 },
  { time: 1734990696, bid: 2.17709, ask: 2.17917 },
  { time: 1734990696, bid: 2.17709, ask: 2.17917 },
  { time: 1734990696, bid: 2.17709, ask: 2.1791 },
  { time: 1734990696, bid: 2.17709, ask: 2.1791 },
  { time: 1734990696, bid: 2.17709, ask: 2.1791 },
  { time: 1734990696, bid: 2.17709, ask: 2.1791 },
  { time: 1734990696, bid: 2.17709, ask: 2.1791 },
  { time: 1734990696, bid: 2.17709, ask: 2.1791 },
  { time: 1734990696, bid: 2.17709, ask: 2.1791 },
  { time: 1734990697, bid: 2.17709, ask: 2.1791 },
  { time: 1734990697, bid: 2.17709, ask: 2.1791 },
  { time: 1734990697, bid: 2.17709, ask: 2.1791 },
  { time: 1734990697, bid: 2.17709, ask: 2.1791 },
  { time: 1734990697, bid: 2.17709, ask: 2.1791 },
  { time: 1734990697, bid: 2.17709, ask: 2.1791 },
  { time: 1734990697, bid: 2.17709, ask: 2.1791 },
  { time: 1734990697, bid: 2.17709, ask: 2.1791 },
  { time: 1734990697, bid: 2.17709, ask: 2.1791 },
  { time: 1734990697, bid: 2.17709, ask: 2.1791 },
  { time: 1734990698, bid: 2.17709, ask: 2.1791 },
  { time: 1734990698, bid: 2.17709, ask: 2.1791 },
  { time: 1734990698, bid: 2.17709, ask: 2.1791 },
  { time: 1734990698, bid: 2.17709, ask: 2.1791 },
  { time: 1734990698, bid: 2.17709, ask: 2.1791 },
  { time: 1734990698, bid: 2.17709, ask: 2.1791 },
  { time: 1734990698, bid: 2.17709, ask: 2.1791 },
  { time: 1734990698, bid: 2.17709, ask: 2.1791 },
  { time: 1734990699, bid: 2.17709, ask: 2.1791 },
  { time: 1734990699, bid: 2.17709, ask: 2.1791 },
  { time: 1734990699, bid: 2.17709, ask: 2.1791 },
  { time: 1734990699, bid: 2.17709, ask: 2.1791 },
  { time: 1734990699, bid: 2.17709, ask: 2.1791 },
  { time: 1734990699, bid: 2.17709, ask: 2.1791 },
  { time: 1734990699, bid: 2.17709, ask: 2.1791 },
  { time: 1734990699, bid: 2.17709, ask: 2.1791 },
  { time: 1734990699, bid: 2.17709, ask: 2.1791 },
  { time: 1734990699, bid: 2.17709, ask: 2.1791 },
  { time: 1734990700, bid: 2.17709, ask: 2.1791 },
  { time: 1734990700, bid: 2.17709, ask: 2.1791 },
  { time: 1734990700, bid: 2.17709, ask: 2.1791 },
  { time: 1734990700, bid: 2.17709, ask: 2.1791 },
  { time: 1734990700, bid: 2.17709, ask: 2.1791 },
  { time: 1734990700, bid: 2.17709, ask: 2.1791 },
  { time: 1734990700, bid: 2.17709, ask: 2.1791 },
  { time: 1734990700, bid: 2.17709, ask: 2.1792 },
  { time: 1734990700, bid: 2.17709, ask: 2.1791 },
  { time: 1734990700, bid: 2.17719, ask: 2.1792 },
  { time: 1734990701, bid: 2.17719, ask: 2.1792 },
  { time: 1734990701, bid: 2.17729, ask: 2.1794000000000002 },
  { time: 1734990701, bid: 2.17729, ask: 2.17937 },
  { time: 1734990701, bid: 2.17729, ask: 2.17937 },
  { time: 1734990701, bid: 2.17729, ask: 2.1793 },
  { time: 1734990701, bid: 2.17729, ask: 2.1793 },
  { time: 1734990701, bid: 2.17729, ask: 2.1793 },
  { time: 1734990701, bid: 2.17729, ask: 2.1793 },
  { time: 1734990701, bid: 2.17729, ask: 2.1793 },
  { time: 1734990702, bid: 2.17729, ask: 2.1793 },
  { time: 1734990702, bid: 2.17729, ask: 2.1793 },
  { time: 1734990702, bid: 2.17729, ask: 2.1793 },
  { time: 1734990702, bid: 2.17729, ask: 2.1793 },
  { time: 1734990702, bid: 2.17729, ask: 2.1793 },
  { time: 1734990702, bid: 2.17729, ask: 2.17949 },
  { time: 1734990702, bid: 2.17729, ask: 2.17949 },
  { time: 1734990702, bid: 2.17729, ask: 2.17949 },
  { time: 1734990702, bid: 2.17729, ask: 2.1793 },
  { time: 1734990703, bid: 2.17729, ask: 2.1793 },
  { time: 1734990703, bid: 2.17729, ask: 2.1793 },
  { time: 1734990703, bid: 2.17729, ask: 2.1793 },
  { time: 1734990703, bid: 2.17729, ask: 2.1793 },
  { time: 1734990703, bid: 2.17729, ask: 2.1793 },
  { time: 1734990703, bid: 2.17729, ask: 2.1793 },
  { time: 1734990703, bid: 2.17729, ask: 2.1793 },
  { time: 1734990703, bid: 2.17729, ask: 2.1793 },
  { time: 1734990703, bid: 2.17729, ask: 2.1793 },
  { time: 1734990703, bid: 2.17729, ask: 2.1793 },
  { time: 1734990704, bid: 2.17729, ask: 2.1793 },
  { time: 1734990704, bid: 2.17729, ask: 2.1793 },
  { time: 1734990704, bid: 2.17729, ask: 2.1793 },
  { time: 1734990704, bid: 2.17729, ask: 2.1793 },
  { time: 1734990704, bid: 2.17729, ask: 2.1793 },
  { time: 1734990704, bid: 2.17729, ask: 2.1793 },
  { time: 1734990704, bid: 2.17729, ask: 2.1793 },
  { time: 1734990704, bid: 2.17729, ask: 2.1793 },
  { time: 1734990704, bid: 2.17729, ask: 2.1793 },
  { time: 1734990704, bid: 2.17729, ask: 2.1793 },
  { time: 1734990705, bid: 2.17729, ask: 2.1793 },
  { time: 1734990705, bid: 2.17729, ask: 2.1793 },
  { time: 1734990705, bid: 2.17729, ask: 2.1793 },
  { time: 1734990705, bid: 2.17729, ask: 2.1793 },
  { time: 1734990705, bid: 2.17729, ask: 2.1793 },
  { time: 1734990705, bid: 2.17729, ask: 2.1793 },
  { time: 1734990705, bid: 2.17729, ask: 2.1793 },
  { time: 1734990705, bid: 2.17729, ask: 2.1793 },
  { time: 1734990705, bid: 2.17729, ask: 2.1793 },
  { time: 1734990706, bid: 2.17729, ask: 2.1793 },
  { time: 1734990706, bid: 2.17706, ask: 2.1791 },
  { time: 1734990706, bid: 2.17701, ask: 2.1791 },
  { time: 1734990706, bid: 2.17698, ask: 2.1791 },
  { time: 1734990706, bid: 2.17702, ask: 2.1791 },
  { time: 1734990706, bid: 2.17698, ask: 2.1791 },
  { time: 1734990706, bid: 2.17698, ask: 2.1791 },
  { time: 1734990706, bid: 2.17698, ask: 2.1791 },
  { time: 1734990706, bid: 2.17704, ask: 2.1791 },
  { time: 1734990707, bid: 2.17709, ask: 2.1791 },
  { time: 1734990707, bid: 2.17709, ask: 2.1791 },
  { time: 1734990707, bid: 2.17709, ask: 2.1791 },
  { time: 1734990707, bid: 2.17709, ask: 2.1791 },
  { time: 1734990707, bid: 2.17709, ask: 2.1791 },
  { time: 1734990707, bid: 2.17709, ask: 2.1791 },
  { time: 1734990707, bid: 2.17709, ask: 2.17916 },
  { time: 1734990707, bid: 2.17709, ask: 2.17917 },
  { time: 1734990707, bid: 2.17709, ask: 2.17917 },
  { time: 1734990707, bid: 2.17709, ask: 2.1791 },
  { time: 1734990708, bid: 2.17709, ask: 2.1791 },
  { time: 1734990708, bid: 2.17709, ask: 2.17917 },
  { time: 1734990708, bid: 2.17709, ask: 2.17917 },
  { time: 1734990708, bid: 2.17709, ask: 2.1791 },
  { time: 1734990708, bid: 2.17709, ask: 2.1791 },
  { time: 1734990708, bid: 2.17709, ask: 2.1791 },
  { time: 1734990708, bid: 2.17709, ask: 2.1791 },
  { time: 1734990708, bid: 2.17709, ask: 2.1791 },
  { time: 1734990708, bid: 2.17709, ask: 2.1791 },
  { time: 1734990709, bid: 2.17709, ask: 2.17918 },
  { time: 1734990709, bid: 2.17709, ask: 2.17918 },
  { time: 1734990709, bid: 2.17709, ask: 2.17918 },
  { time: 1734990709, bid: 2.17709, ask: 2.1791 },
  { time: 1734990709, bid: 2.17709, ask: 2.1791 },
  { time: 1734990709, bid: 2.17709, ask: 2.1791 },
  { time: 1734990709, bid: 2.17709, ask: 2.1791 },
  { time: 1734990709, bid: 2.17709, ask: 2.17919 },
  { time: 1734990709, bid: 2.17709, ask: 2.17919 },
  { time: 1734990709, bid: 2.17709, ask: 2.17919 },
  { time: 1734990710, bid: 2.17709, ask: 2.17919 },
  { time: 1734990710, bid: 2.17709, ask: 2.17928 },
  { time: 1734990710, bid: 2.17709, ask: 2.1792 },
  { time: 1734990710, bid: 2.17709, ask: 2.1791 },
  { time: 1734990710, bid: 2.17709, ask: 2.1791 },
  { time: 1734990710, bid: 2.17709, ask: 2.1791 },
  { time: 1734990710, bid: 2.17709, ask: 2.1791 },
  { time: 1734990710, bid: 2.17719, ask: 2.17929 },
  { time: 1734990710, bid: 2.17719, ask: 2.17929 },
  { time: 1734990711, bid: 2.17742, ask: 2.1796 },
  { time: 1734990711, bid: 2.17756, ask: 2.1796 },
  { time: 1734990711, bid: 2.17755, ask: 2.1796 },
  { time: 1734990711, bid: 2.17739, ask: 2.1794000000000002 },
  { time: 1734990711, bid: 2.17739, ask: 2.1794000000000002 },
  { time: 1734990711, bid: 2.17739, ask: 2.1794000000000002 },
  { time: 1734990711, bid: 2.17739, ask: 2.1795 },
  { time: 1734990711, bid: 2.17749, ask: 2.1795 },
  { time: 1734990711, bid: 2.17749, ask: 2.1796 },
  { time: 1734990711, bid: 2.17749, ask: 2.1796 },
  { time: 1734990712, bid: 2.17749, ask: 2.1795 },
  { time: 1734990712, bid: 2.17749, ask: 2.1795 },
  { time: 1734990712, bid: 2.17749, ask: 2.1795 },
  { time: 1734990712, bid: 2.17749, ask: 2.17958 },
  { time: 1734990712, bid: 2.17749, ask: 2.1795 },
  { time: 1734990712, bid: 2.17749, ask: 2.1795 },
  { time: 1734990712, bid: 2.17749, ask: 2.1795 },
  { time: 1734990712, bid: 2.17749, ask: 2.1795 },
  { time: 1734990712, bid: 2.17749, ask: 2.1795 },
  { time: 1734990713, bid: 2.17749, ask: 2.1795 },
  { time: 1734990713, bid: 2.17749, ask: 2.17959 },
  { time: 1734990713, bid: 2.17749, ask: 2.17959 },
  { time: 1734990713, bid: 2.17759, ask: 2.1796 },
  { time: 1734990713, bid: 2.17759, ask: 2.1796 },
  { time: 1734990713, bid: 2.17759, ask: 2.1796 },
  { time: 1734990713, bid: 2.17759, ask: 2.1796 },
  { time: 1734990713, bid: 2.17759, ask: 2.1796 },
  { time: 1734990713, bid: 2.17759, ask: 2.1796 },
  { time: 1734990713, bid: 2.17759, ask: 2.1796 },
  { time: 1734990714, bid: 2.17759, ask: 2.1796 },
  { time: 1734990714, bid: 2.17759, ask: 2.1796 },
  { time: 1734990714, bid: 2.17759, ask: 2.1796 },
  { time: 1734990714, bid: 2.17759, ask: 2.1796 },
  { time: 1734990714, bid: 2.1775, ask: 2.1796 },
  { time: 1734990714, bid: 2.17749, ask: 2.1796 },
  { time: 1734990714, bid: 2.17749, ask: 2.1795 },
  { time: 1734990714, bid: 2.17749, ask: 2.1795 },
  { time: 1734990714, bid: 2.17749, ask: 2.1795 },
  { time: 1734990715, bid: 2.17749, ask: 2.1795 },
  { time: 1734990715, bid: 2.17749, ask: 2.1795 },
  { time: 1734990715, bid: 2.17749, ask: 2.1795 },
  { time: 1734990715, bid: 2.17749, ask: 2.1795 },
  { time: 1734990715, bid: 2.17749, ask: 2.1795 },
  { time: 1734990715, bid: 2.17749, ask: 2.1795 },
  { time: 1734990715, bid: 2.17749, ask: 2.1795 },
  { time: 1734990715, bid: 2.17749, ask: 2.1795 },
  { time: 1734990715, bid: 2.17749, ask: 2.1795 },
  { time: 1734990715, bid: 2.17749, ask: 2.1795 },
  { time: 1734990716, bid: 2.17749, ask: 2.1795 },
  { time: 1734990716, bid: 2.17749, ask: 2.1795 },
  { time: 1734990716, bid: 2.17749, ask: 2.1795 },
  { time: 1734990716, bid: 2.17769, ask: 2.17979 },
  { time: 1734990716, bid: 2.17769, ask: 2.1797 },
  { time: 1734990716, bid: 2.17769, ask: 2.1797 },
  { time: 1734990716, bid: 2.17769, ask: 2.1797 },
  { time: 1734990716, bid: 2.17769, ask: 2.1797 },
  { time: 1734990716, bid: 2.17769, ask: 2.1797 },
  { time: 1734990717, bid: 2.17769, ask: 2.1797 },
  { time: 1734990717, bid: 2.17769, ask: 2.1797 },
  { time: 1734990717, bid: 2.17769, ask: 2.1797 },
  { time: 1734990717, bid: 2.17769, ask: 2.1797 },
  { time: 1734990717, bid: 2.17769, ask: 2.1797 },
  { time: 1734990717, bid: 2.17769, ask: 2.1797 },
  { time: 1734990717, bid: 2.17769, ask: 2.1797 },
  { time: 1734990717, bid: 2.17769, ask: 2.1797 },
  { time: 1734990717, bid: 2.17769, ask: 2.1797 },
  { time: 1734990718, bid: 2.17769, ask: 2.1797 },
  { time: 1734990718, bid: 2.17769, ask: 2.1797 },
  { time: 1734990718, bid: 2.17769, ask: 2.1797 },
  { time: 1734990718, bid: 2.17769, ask: 2.1797 },
  { time: 1734990718, bid: 2.17769, ask: 2.1797 },
  { time: 1734990718, bid: 2.17769, ask: 2.1797 },
  { time: 1734990718, bid: 2.17769, ask: 2.1797 },
  { time: 1734990718, bid: 2.17769, ask: 2.1797 },
  { time: 1734990718, bid: 2.17769, ask: 2.1797 },
  { time: 1734990718, bid: 2.17769, ask: 2.1797 },
  { time: 1734990718, bid: 2.17769, ask: 2.1797 },
  { time: 1734990719, bid: 2.17769, ask: 2.1797 },
  { time: 1734990719, bid: 2.17769, ask: 2.1797 },
  { time: 1734990719, bid: 2.17769, ask: 2.1797 },
  { time: 1734990719, bid: 2.17769, ask: 2.1797 },
  { time: 1734990719, bid: 2.17769, ask: 2.1797 },
  { time: 1734990719, bid: 2.17769, ask: 2.1797 },
  { time: 1734990719, bid: 2.17769, ask: 2.1797 },
  { time: 1734990719, bid: 2.17769, ask: 2.1797 },
  { time: 1734990719, bid: 2.17749, ask: 2.1795 },
  { time: 1734990720, bid: 2.17749, ask: 2.1795 },
  { time: 1734990720, bid: 2.17743, ask: 2.1795 },
  { time: 1734990720, bid: 2.1774, ask: 2.1795 },
  { time: 1734990720, bid: 2.17731, ask: 2.1794000000000002 },
  { time: 1734990720, bid: 2.17731, ask: 2.1794000000000002 },
  { time: 1734990720, bid: 2.17739, ask: 2.1794000000000002 },
  { time: 1734990720, bid: 2.17739, ask: 2.1794000000000002 },
  { time: 1734990720, bid: 2.17739, ask: 2.1795 },
  { time: 1734990720, bid: 2.17739, ask: 2.1794000000000002 },
  { time: 1734990721, bid: 2.17739, ask: 2.1794000000000002 },
  { time: 1734990721, bid: 2.17739, ask: 2.17949 },
  { time: 1734990721, bid: 2.17739, ask: 2.17949 },
  { time: 1734990721, bid: 2.1773, ask: 2.1794000000000002 },
  { time: 1734990721, bid: 2.17731, ask: 2.1794000000000002 },
  { time: 1734990721, bid: 2.17731, ask: 2.1794000000000002 },
  { time: 1734990721, bid: 2.17739, ask: 2.1795 },
  { time: 1734990721, bid: 2.17743, ask: 2.1795 },
  { time: 1734990721, bid: 2.17743, ask: 2.1795 },
  { time: 1734990721, bid: 2.17744, ask: 2.1795 },
  { time: 1734990722, bid: 2.17744, ask: 2.1795 },
  { time: 1734990722, bid: 2.17749, ask: 2.1795 },
  { time: 1734990722, bid: 2.17729, ask: 2.1793 },
  { time: 1734990722, bid: 2.17716, ask: 2.1792 },
  { time: 1734990722, bid: 2.17669, ask: 2.1788 },
  { time: 1734990722, bid: 2.17669, ask: 2.1787 },
  { time: 1734990722, bid: 2.1764, ask: 2.1786 },
  { time: 1734990722, bid: 2.17639, ask: 2.1784 },
  { time: 1734990722, bid: 2.17639, ask: 2.1784 },
  { time: 1734990722, bid: 2.17639, ask: 2.1784 },
  { time: 1734990723, bid: 2.17639, ask: 2.1784 },
  { time: 1734990723, bid: 2.17619, ask: 2.1782 },
  { time: 1734990723, bid: 2.17599, ask: 2.178 },
  { time: 1734990723, bid: 2.17599, ask: 2.178 },
  { time: 1734990723, bid: 2.17577, ask: 2.1779 },
  { time: 1734990723, bid: 2.17579, ask: 2.1779 },
  { time: 1734990723, bid: 2.17577, ask: 2.1779 },
  { time: 1734990723, bid: 2.17585, ask: 2.1779 },
  { time: 1734990723, bid: 2.17585, ask: 2.1779 },
  { time: 1734990723, bid: 2.1757400000000002, ask: 2.1779 },
  { time: 1734990724, bid: 2.1757400000000002, ask: 2.1779 },
  { time: 1734990724, bid: 2.17553, ask: 2.1777 },
  { time: 1734990724, bid: 2.17561, ask: 2.1777 },
  { time: 1734990724, bid: 2.17589, ask: 2.1779 },
  { time: 1734990724, bid: 2.17581, ask: 2.178 },
  { time: 1734990724, bid: 2.17583, ask: 2.178 },
  { time: 1734990724, bid: 2.17583, ask: 2.178 },
  { time: 1734990724, bid: 2.17583, ask: 2.178 },
  { time: 1734990724, bid: 2.17584, ask: 2.178 },
  { time: 1734990725, bid: 2.17584, ask: 2.178 },
  { time: 1734990725, bid: 2.17584, ask: 2.178 },
  { time: 1734990725, bid: 2.17584, ask: 2.178 },
  { time: 1734990725, bid: 2.17584, ask: 2.178 },
  { time: 1734990725, bid: 2.17584, ask: 2.178 },
  { time: 1734990725, bid: 2.17599, ask: 2.178 },
  { time: 1734990725, bid: 2.17599, ask: 2.17815 },
  { time: 1734990725, bid: 2.17629, ask: 2.1783 },
  { time: 1734990725, bid: 2.17629, ask: 2.1785 },
  { time: 1734990725, bid: 2.17629, ask: 2.1785 },
  { time: 1734990726, bid: 2.17629, ask: 2.1785 },
  { time: 1734990726, bid: 2.17629, ask: 2.17848 },
  { time: 1734990726, bid: 2.17629, ask: 2.17848 },
  { time: 1734990726, bid: 2.17629, ask: 2.17848 },
  { time: 1734990726, bid: 2.17629, ask: 2.17848 },
  { time: 1734990726, bid: 2.17629, ask: 2.17848 },
  { time: 1734990726, bid: 2.17629, ask: 2.17849 },
  { time: 1734990726, bid: 2.17629, ask: 2.17849 },
  { time: 1734990726, bid: 2.17649, ask: 2.17859 },
  { time: 1734990726, bid: 2.17669, ask: 2.1787 },
  { time: 1734990727, bid: 2.17669, ask: 2.1787 },
  { time: 1734990727, bid: 2.17669, ask: 2.1787 },
  { time: 1734990727, bid: 2.17669, ask: 2.1787 },
  { time: 1734990727, bid: 2.17669, ask: 2.1787 },
  { time: 1734990727, bid: 2.17669, ask: 2.1787 },
  { time: 1734990727, bid: 2.17649, ask: 2.1785 },
  { time: 1734990727, bid: 2.17641, ask: 2.1785 },
  { time: 1734990727, bid: 2.1764, ask: 2.1785 },
  { time: 1734990727, bid: 2.17639, ask: 2.1785 },
  { time: 1734990728, bid: 2.17639, ask: 2.1785 },
  { time: 1734990728, bid: 2.17629, ask: 2.17846 },
  { time: 1734990728, bid: 2.17629, ask: 2.17857 },
  { time: 1734990728, bid: 2.17629, ask: 2.17846 },
  { time: 1734990728, bid: 2.17629, ask: 2.1783 },
  { time: 1734990728, bid: 2.17629, ask: 2.1783 },
  { time: 1734990728, bid: 2.17629, ask: 2.1783 },
  { time: 1734990728, bid: 2.17629, ask: 2.1783 },
  { time: 1734990728, bid: 2.17629, ask: 2.1783 },
  { time: 1734990728, bid: 2.17629, ask: 2.1783 },
  { time: 1734990729, bid: 2.17639, ask: 2.1784 },
  { time: 1734990729, bid: 2.17639, ask: 2.17848 },
  { time: 1734990729, bid: 2.17639, ask: 2.17848 },
  { time: 1734990729, bid: 2.17639, ask: 2.17848 },
  { time: 1734990729, bid: 2.17639, ask: 2.17848 },
  { time: 1734990729, bid: 2.17659, ask: 2.17869 },
  { time: 1734990729, bid: 2.17669, ask: 2.1787 },
  { time: 1734990729, bid: 2.17659, ask: 2.1787 },
  { time: 1734990729, bid: 2.17669, ask: 2.1787 },
  { time: 1734990730, bid: 2.17669, ask: 2.1787 },
  { time: 1734990730, bid: 2.17669, ask: 2.1787 },
  { time: 1734990730, bid: 2.17669, ask: 2.1787 },
  { time: 1734990730, bid: 2.17669, ask: 2.1787 },
  { time: 1734990730, bid: 2.17669, ask: 2.1787 },
  { time: 1734990730, bid: 2.17669, ask: 2.1787 },
  { time: 1734990730, bid: 2.17669, ask: 2.1787 },
  { time: 1734990730, bid: 2.17669, ask: 2.1787 },
  { time: 1734990730, bid: 2.17669, ask: 2.1787 },
  { time: 1734990730, bid: 2.17669, ask: 2.1787 },
  { time: 1734990731, bid: 2.17669, ask: 2.1787 },
  { time: 1734990731, bid: 2.17669, ask: 2.1787 },
  { time: 1734990731, bid: 2.17669, ask: 2.1787 },
  { time: 1734990731, bid: 2.17669, ask: 2.1787 },
  { time: 1734990731, bid: 2.17669, ask: 2.1787 },
  { time: 1734990731, bid: 2.17669, ask: 2.1787 },
  { time: 1734990731, bid: 2.17669, ask: 2.1787 },
  { time: 1734990731, bid: 2.17669, ask: 2.1787 },
  { time: 1734990731, bid: 2.17669, ask: 2.1787 },
  { time: 1734990732, bid: 2.17669, ask: 2.1787 },
  { time: 1734990732, bid: 2.17634, ask: 2.1784 },
  { time: 1734990732, bid: 2.17649, ask: 2.1785 },
  { time: 1734990732, bid: 2.17649, ask: 2.1785 },
  { time: 1734990732, bid: 2.17641, ask: 2.1785 },
  { time: 1734990732, bid: 2.17641, ask: 2.1785 },
  { time: 1734990732, bid: 2.17641, ask: 2.1785 },
  { time: 1734990732, bid: 2.1764, ask: 2.1785 },
  { time: 1734990732, bid: 2.17634, ask: 2.1784 },
  { time: 1734990732, bid: 2.17632, ask: 2.1784 },
  { time: 1734990733, bid: 2.17639, ask: 2.1784 },
  { time: 1734990733, bid: 2.17639, ask: 2.1784 },
  { time: 1734990733, bid: 2.17639, ask: 2.1784 },
  { time: 1734990733, bid: 2.17639, ask: 2.1784 },
  { time: 1734990733, bid: 2.17639, ask: 2.1784 },
  { time: 1734990733, bid: 2.17639, ask: 2.1784 },
  { time: 1734990733, bid: 2.17639, ask: 2.1784 },
  { time: 1734990733, bid: 2.17639, ask: 2.1784 },
  { time: 1734990733, bid: 2.17639, ask: 2.1784 },
  { time: 1734990734, bid: 2.17639, ask: 2.1784 },
  { time: 1734990734, bid: 2.17639, ask: 2.1784 },
  { time: 1734990734, bid: 2.17639, ask: 2.1784 },
  { time: 1734990734, bid: 2.17639, ask: 2.1784 },
  { time: 1734990734, bid: 2.17639, ask: 2.1784 },
  { time: 1734990734, bid: 2.17639, ask: 2.1784 },
  { time: 1734990734, bid: 2.17639, ask: 2.1784 },
  { time: 1734990734, bid: 2.17639, ask: 2.1784 },
  { time: 1734990734, bid: 2.17639, ask: 2.1784 },
];
export default TradePanel;
