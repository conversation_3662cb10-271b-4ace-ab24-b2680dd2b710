import classNames from "classnames";
import {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useLayoutEffect,
  useRef,
  useState,
} from "react";
import {
  computePaddedMinMax,
  createDrawPoints,
  createStaticPoints,
  flipY,
  generateCurvePath,
} from "../utils/functions";
import { StoreRef, Tick, TickData } from "../utils/interfaces";
import ChartBg from "./chart-bg";
import { ChartHandle } from "./chart-handle";
import CurvedLineSVG from "./chart-svg";
import ChartToolbar from "./chart-toolbar";
import OrderLine from "./order-line";
import PriceLine from "./price-line";
import { lastElem } from "@/lib/utils";

const TIME_FRAME = 1; // MINS
const ASPECT_RATIO = 16 / 11;

interface Props {
  staticPrices: { price: number; time: number }[];
  handleData: {
    value: number;
    onChange: (value: number) => void;
    type: "take-profit" | "stop-loss";
  }[];
  currentOrderData?: {
    price: number;
    type: "buy" | "sell";
    stopLoss?: number;
    takeProfit?: number;
  };
  brokerData?: {
    name: string;
    symbols: string[];
    currentSymbol: string;
  };
  onSymbolChange: (symbol: string) => void;
}

export interface TradingChartRef {
  onTick: (tickData: TickData) => void;
  reset: () => void;
}

const TradingChart: React.ForwardRefRenderFunction<TradingChartRef, Props> = (
  {
    staticPrices,
    handleData = [],
    currentOrderData,
    brokerData,
    onSymbolChange,
  },
  ref
) => {
  const root = useRef<HTMLDivElement>(null);

  const [currentPriceTop, setCurrentPriceTop] = useState(0);
  const [currentOrderTop, setCurrentOrderTop] = useState(0);
  const [currentPrice, setCurrentPrice] = useState(0);
  const [redrawCount, setRedrawCount] = useState(0);
  const [resizeCount, setResizeCount] = useState(0);

  const [zoomX, setZoomX] = useState(0.7);
  const [zoomY, setZoomY] = useState(1);
  const [scaleY] = useState(1);

  const storeRef = useRef<StoreRef>({
    dimensions: {
      width: 250,
      height: (1 / ASPECT_RATIO) * 250,
    },
    tickCount: 1,
    redrawCount: 0,
  });

  const reset = () => {
    setRedrawCount(0);
    setCurrentOrderTop(0);
    setCurrentPrice(0);
    setCurrentPriceTop(0);
    setZoomX(0.7);
    setZoomY(1);
    storeRef.current = {
      dimensions: storeRef.current.dimensions,
      tickCount: 1,
      redrawCount: 0,
    };
    svgRef.current?.setAttribute("d", "");
  };

  const svgRef = useRef<SVGPathElement>(null);
  const isLosing =
    currentOrderData?.type === "sell"
      ? currentPrice < currentOrderData.price
      : currentPrice > (currentOrderData?.price ?? 0);

  useEffect(() => {
    if (staticPrices.length === 0) return;

    let { pricePoints, max, min, paddedMax, paddedMin } = createStaticPoints({
      computeStaticMinMax: false,
      ticks: staticPrices,
      timeFrame: TIME_FRAME,
      zoomY,
    });

    // console.log(min, max);
    // console.log(pricePoints)
    // return

    // console.log(
    //   lastElem(pricePoints),
    //   lastElem(staticPrices)
    // );

    if (redrawCount > 0) {
      pricePoints = storeRef.current.pricePoints!;
      console.log("redraw");
    }

    console.log("past here");

    const drawPoints = createDrawPoints(
      {
        dimensions: storeRef.current?.dimensions,
        isRedraw: true,
        max: paddedMax,
        min: paddedMin,
        scaleY,
        zoomX,
        pricePoints,
      },
      () => setRedrawCount((prev) => prev + 1)
    );

    const path = generateCurvePath(drawPoints);
    if (path) svgRef.current?.setAttribute("d", path);

    storeRef.current = {
      ...storeRef.current,
      currentMax: max,
      currentMin: min,
      drawPoints,
      pricePoints,
    };
  }, [zoomX, zoomY, handleData, currentOrderData, resizeCount, staticPrices]);

  useEffect(() => {
    if (currentOrderData) {
      const {
        currentMax = 0,
        currentMin = 0,
        dimensions: { height } = { width: 0, height: 0 },
      } = storeRef.current;
      const delta = currentMax - currentMin;
      const orderTop = ((currentOrderData.price - currentMin) / delta) * height;

      storeRef.current.currentOrderTop = orderTop;
      setCurrentOrderTop(orderTop);
    }
  }, [currentOrderData, zoomX, zoomY, redrawCount, resizeCount]);

  useLayoutEffect(() => {
    if (root.current) {
      const resizeObserver = new ResizeObserver((entries) => {
        for (let entry of entries) {
          if (entry.contentBoxSize) {
            const width = entry.contentBoxSize[0].inlineSize;
            const height = entry.contentBoxSize[0].blockSize;

            storeRef.current.dimensions = { width, height };
            setResizeCount((prev) => prev + 1);
          }
        }
      });
      resizeObserver.observe(root.current);
    }
  }, []);

  const handleTick = (tickData: TickData) => {
    let {
      currentMax = 0,
      currentMin = 0,
      drawPoints = [],
      pricePoints = [],
      currentPriceBucket = [],
      dimensions = { width: 0, height: 0 },
      lastTickMinute = 0,
    } = storeRef.current;

    if (drawPoints.length === 0) return;

    const { price, time } = tickData;

    setCurrentPrice(price);

    const { paddedMax, paddedMin } = computePaddedMinMax(
      currentMin,
      currentMax,
      zoomY
    );

    const delta = paddedMax - paddedMin;
    const height = dimensions.height;
    // const width = dimensions.width;

    const formattedDateTime = new Date(time * 1000);
    const minute = formattedDateTime.getMinutes();

    let y = 0;
    currentPriceBucket.push({ time, price });

    // if we are in new frame
    if (minute % TIME_FRAME === 0 && lastTickMinute !== minute) {
      storeRef.current.lastTickMinute = minute;

      const bucketPricesCopy = [...currentPriceBucket];
      const pricesPointsCopy = [...pricePoints];

      const avg =
        bucketPricesCopy.reduce((a, b) => a + b.price, 0) /
        bucketPricesCopy.length;

      const tick: Tick = {
        time: lastElem(bucketPricesCopy)!.time,
        price: avg,
      };

      //
      pricesPointsCopy.splice(0, 1)!;
      pricesPointsCopy[pricesPointsCopy.length - 1] = tick;
      pricesPointsCopy?.push(tick);

      const newDrawPoints = createDrawPoints(
        {
          dimensions: storeRef.current?.dimensions,
          isRedraw: true,
          max: paddedMax,
          min: paddedMin,
          scaleY,
          pricePoints: pricesPointsCopy || [],
          zoomX,
        },
        () => setRedrawCount((prev) => prev + 1)
      );

      const path = generateCurvePath(newDrawPoints);

      if (path) svgRef.current?.setAttribute("d", path);

      storeRef.current = {
        ...storeRef.current,
        drawPoints: newDrawPoints,
        pricePoints: pricesPointsCopy,
        currentPriceBucket: [],
        tickCount: 1,
      };

      y = lastElem(newDrawPoints)!.y;
      setCurrentPriceTop(y * scaleY);
    } else {
      y = ((price - paddedMin) / delta) * height;
      y = flipY(y, height);

      // if y crosses boundaries
      if (y < 0 || y > dimensions.height) {
        const currentDelta = currentMax - currentMin;

        const dir = y > dimensions.height ? 1 : -1;
        let newMax = price + (currentDelta / 2) * dir;
        let newMin = price - (currentDelta / 2) * dir;

        const { paddedMax, paddedMin } = computePaddedMinMax(
          newMax,
          newMin,
          zoomY
        );
        const newDelta = paddedMax - paddedMin;

        const newDrawPoints = createDrawPoints(
          {
            dimensions: storeRef.current?.dimensions,
            isRedraw: true,
            max: paddedMax,
            min: paddedMin,
            scaleY,
            pricePoints,
            zoomX,
          },
          () => setRedrawCount((prev) => prev + 1)
        );

        y = ((price - newMin) / newDelta) * height;
        y = flipY(y, height);
        newDrawPoints[newDrawPoints.length - 1].y = y * scaleY;

        const path = generateCurvePath(newDrawPoints);
        if (path) svgRef.current?.setAttribute("d", path);

        storeRef.current.currentMax = newMax;
        storeRef.current.currentMin = newMin;
      } else {
        drawPoints[drawPoints.length - 1].y = y * scaleY;
        const path = generateCurvePath(drawPoints);

        if (path) svgRef.current?.setAttribute("d", path);
      }
      storeRef.current.tickCount!++;
      setCurrentPriceTop(y * scaleY);
    }
  };

  useImperativeHandle(ref, () => ({ onTick: handleTick, reset }), [
    zoomX,
    scaleY,
    zoomY,
  ]);

  const handleZoom = (dir: "out" | "in") => {
    const xZoomValues = [0.4, 0.5, 0.6, 0.7, 0.8];
    const yZoomValues = [0, 0.5, 1, 1.5, 2, 2.5];

    if (dir === "out") {
      const index = xZoomValues.indexOf(zoomX);
      if (index > 0) {
        setZoomX(xZoomValues[index - 1]);
      }

      const indexY = yZoomValues.indexOf(zoomY);
      if (indexY < yZoomValues.length - 1) {
        setZoomY(yZoomValues[indexY + 1]);
      }
    } else {
      const index = xZoomValues.indexOf(zoomX);
      if (index < xZoomValues.length - 1) {
        setZoomX(xZoomValues[index + 1]);
      }

      const indexY = yZoomValues.indexOf(zoomY);
      if (indexY > 0) {
        setZoomY(yZoomValues[indexY - 1]);
      }
    }
  };

  const legendData = {
    "current price": {
      active: true,
      color: "bg-white",
    },
    "current order": {
      active: Boolean(currentOrderData?.price),
      color: "bg-blue-400",
    },
    "stop loss": {
      active: Boolean(currentOrderData?.stopLoss),
      color: "bg-orange-300",
    },
    "take profit": {
      active: Boolean(currentOrderData?.takeProfit),
      color: "bg-green-400",
    },
  };

  return (
    <>
      <div
        ref={root}
        className={`min-w-[250px] w-full aspect-[16/11] bg-[#060709] relative overflow-hidden rounded-3xl`}
      >
        <ChartToolbar
          onSymbolChange={onSymbolChange}
          onZoom={handleZoom}
          brokerData={brokerData}
        />
        <PriceLine {...{ currentPriceTop, currentPrice, zoomX }} />
        <CurvedLineSVG ref={svgRef} />
        <ChartBg zoomX={zoomX} zoomY={zoomY} />
        <OrderLine
          {...{
            isLosing,
            currentOrderPrice: currentOrderData?.price,
            currentOrderTop,
            zoomX,
          }}
        />
        {handleData.map((item, index) => (
          <ChartHandle
            key={index}
            onChange={(v) => item.onChange(v)}
            startValue={item.value}
            storeRef={storeRef}
            rootRef={root}
            aspectRatio={ASPECT_RATIO}
            invertHandle={item.type === "stop-loss"}
            color={item.type === "stop-loss" ? "orange" : "green"}
            deps={[redrawCount]}
          />
        ))}
      </div>
      <div className="flex justify-between  items-center px-5 py-2.5">
        {Object.entries(legendData).map(([key, { active, color }]) =>
          active ? (
            <div key={key} className="flex items-center gap-[5px]">
              <div
                className={classNames(
                  "w-[10px] h-[10px] rounded-[2px] ",
                  color
                )}
              ></div>
              <span className="text-[11px] text-[#4A4C54] tracking-tight capitalize">
                {key}
              </span>
            </div>
          ) : null
        )}
      </div>
    </>
  );
};

export default forwardRef(TradingChart);
