interface Props {
  currentPriceTop: number;
  currentPrice: number;

  zoomX: number;
}
const PriceLine: React.FC<Props> = ({
  currentPriceTop,
  currentPrice,
  zoomX,
}) => {
  return (
    <>
      {currentPriceTop > 0 && (
        <div
          className="w-full absolute h-[1px] border-t border-dashed border-white border-opacity-40  left-0"
          style={{ top: currentPriceTop }}
        >
          <div className="h-5 w-fit px-[5px] text-[11px] text-white flex items-center justify-center rounded-full bg-white bg-opacity-10 backdrop-blur-sm -translate-y-1/2 right-[2%] absolute">
            {currentPrice.toFixed(5)}
          </div>
          <div
            className={
              "h-5 w-5 -translate-y-1/2 translate-x-1/2 flex items-center justify-center absolute rounded-full"
            }
            style={{ right: `${(1 - zoomX) * 100}%` }}
          >
            <div className="bg-white w-full h-full rounded-full bg-opacity-5 absolute pulse"></div>
            <div className="w-[5px] h-[5px] bg-white"></div>
          </div>
        </div>
      )}
    </>
  );
};
export default PriceLine;
