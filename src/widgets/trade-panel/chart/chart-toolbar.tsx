import Combobox from "@/components/ui/combobox";

interface Props {
  brokerData?: {
    name: string;
    symbols: string[];
    currentSymbol: string;
  };
  onZoom: (dir: "in" | "out") => void;
  onSymbolChange: (symbol: string) => void;
}
const ChartToolbar: React.FC<Props> = ({ brokerData, onZoom, onSymbolChange }) => {
  const showCurrencies = () => {};
  return (
    <div className="absolute w-full top-0 left-0 z-[9] flex items-center justify-between p-[10px] box-border">
      <div className="h-[30px] flex gap-[5px] items-center">
        <Combobox
          items={
            brokerData?.symbols?.map((symbol) => ({
              value: symbol,
              label: symbol,
            })) ?? []
          }
          onChange={onSymbolChange}
        >
          <button
            onClick={showCurrencies}
            className="px-2.5 h-full gap-[5px] font-medium flex items-center rounded-full bg-white bg-opacity-5 hover:bg-opacity-10 duration-500 backdrop-blur-sm tracking-tight text-opacity-90 text-[11px] text-white"
          >
            <span>{brokerData?.currentSymbol ?? "-"}</span>
            {/* prettier-ignore */}
            <svg width="10" height="11" viewBox="0 0 10 11" fill="none" >
            <g clipPath="url(#clip0_5417_1173)">
            <path d="M8.65953 3.37194L5.20375 6.82772L1.74798 3.37194C1.5945 3.21847 1.34572 3.21847 1.19225 3.37194C1.03878 3.52541 1.03878 3.77419 1.19225 3.92766L4.92589 7.6613C5.00273 7.73814 5.10314 7.77646 5.20375 7.77646C5.30437 7.77646 5.40478 7.73814 5.48162 7.6613L9.21526 3.92766C9.36873 3.77419 9.36873 3.52541 9.21526 3.37194C9.06178 3.21847 8.813 3.21847 8.65953 3.37194Z" fill="#EBECEF"/>
            </g>
            <defs>
            <clipPath id="clip0_5417_1173">
            <rect width="9.43235" height="9.43235" fill="white" transform="translate(0.487793 0.898438)"/>
            </clipPath>
            </defs>
        </svg>
          </button>
        </Combobox>
        <div className="px-2.5 h-full flex font-medium items-center rounded-full bg-white bg-opacity-5 backdrop-blur-sm tracking-tight text-opacity-90 text-[11px] text-white">
          {brokerData?.name ?? "No Broker"}
        </div>
      </div>

      <div className="flex gap-[5px]">
        <button
          onClick={() => onZoom("in")}
          className="w-[30px] h-[30px] opacity-80 hover:opacity-100 duration-500"
        >
          {/* prettier-ignore */}
          <svg width="33" height="32" viewBox="0 0 33 32" fill="none" >
            <rect x="0.732422" y="0.00976562" width="31.4412" height="31.4412" rx="15.7206" fill="#CBCED7" fillOpacity="0.1"/>
            <path d="M15.2065 8.28027C11.7904 8.28027 9.00342 11.0673 9.00342 14.4834C9.00342 17.8995 11.7904 20.6865 15.2065 20.6865C16.6168 20.6865 17.9046 20.1931 18.9486 19.395L22.4791 22.9255C22.5553 23.0049 22.6466 23.0682 22.7476 23.1119C22.8486 23.1555 22.9573 23.1786 23.0673 23.1797C23.1774 23.1809 23.2865 23.16 23.3884 23.1184C23.4903 23.0768 23.5828 23.0153 23.6606 22.9375C23.7385 22.8597 23.8 22.7671 23.8416 22.6653C23.8831 22.5634 23.904 22.4542 23.9029 22.3442C23.9018 22.2342 23.8787 22.1254 23.835 22.0244C23.7914 21.9234 23.728 21.8321 23.6486 21.7559L20.1182 18.2255C20.9163 17.1815 21.4097 15.8936 21.4097 14.4834C21.4097 11.0673 18.6226 8.28027 15.2065 8.28027ZM15.2065 9.93444C17.7287 9.93444 19.7555 11.9613 19.7555 14.4834C19.7555 17.0055 17.7287 19.0324 15.2065 19.0324C12.6844 19.0324 10.6576 17.0055 10.6576 14.4834C10.6576 11.9613 12.6844 9.93444 15.2065 9.93444ZM15.1944 11.5773C14.9752 11.5805 14.7662 11.6706 14.6134 11.8278C14.4606 11.985 14.3765 12.1965 14.3795 12.4157V13.6563H13.1388C13.0292 13.6548 12.9204 13.675 12.8187 13.7159C12.717 13.7568 12.6244 13.8174 12.5464 13.8944C12.4683 13.9713 12.4064 14.063 12.3641 14.1642C12.3218 14.2653 12.3 14.3738 12.3 14.4834C12.3 14.593 12.3218 14.7015 12.3641 14.8026C12.4064 14.9038 12.4683 14.9955 12.5464 15.0724C12.6244 15.1494 12.717 15.2101 12.8187 15.2509C12.9204 15.2918 13.0292 15.312 13.1388 15.3105H14.3795V16.5511C14.3779 16.6607 14.3982 16.7695 14.439 16.8712C14.4799 16.9729 14.5406 17.0655 14.6175 17.1436C14.6945 17.2216 14.7862 17.2836 14.8873 17.3259C14.9884 17.3682 15.0969 17.39 15.2065 17.39C15.3162 17.39 15.4247 17.3682 15.5258 17.3259C15.6269 17.2836 15.7186 17.2216 15.7956 17.1436C15.8725 17.0655 15.9332 16.9729 15.9741 16.8712C16.0149 16.7695 16.0352 16.6607 16.0336 16.5511V15.3105H17.2743C17.3839 15.312 17.4927 15.2918 17.5944 15.2509C17.6961 15.2101 17.7886 15.1494 17.8667 15.0724C17.9448 14.9955 18.0067 14.9038 18.049 14.8026C18.0913 14.7015 18.1131 14.593 18.1131 14.4834C18.1131 14.3738 18.0913 14.2653 18.049 14.1642C18.0067 14.063 17.9448 13.9713 17.8667 13.8944C17.7886 13.8174 17.6961 13.7568 17.5944 13.7159C17.4927 13.675 17.3839 13.6548 17.2743 13.6563H16.0336V12.4157C16.0351 12.3051 16.0145 12.1953 15.9728 12.0929C15.9312 11.9904 15.8694 11.8974 15.7912 11.8192C15.7129 11.741 15.6198 11.6794 15.5173 11.6378C15.4148 11.5963 15.305 11.5757 15.1944 11.5773Z" fill="#EBECEF"/>
        </svg>
        </button>
        <button
          onClick={() => onZoom("out")}
          className="w-[30px] h-[30px] opacity-80 hover:opacity-100 duration-500"
        >
          {/* prettier-ignore */}
          <svg width="32" height="32" viewBox="0 0 32 32" fill="none" >
            <rect x="0.155762" y="0.00976562" width="31.4412" height="31.4412" rx="15.7206" fill="#CBCED7" fillOpacity="0.1"/>
            <path d="M14.6296 8.28027C11.2135 8.28027 8.42651 11.0673 8.42651 14.4834C8.42651 17.8995 11.2135 20.6865 14.6296 20.6865C16.0399 20.6865 17.3277 20.1931 18.3717 19.395L21.9022 22.9255C21.9784 23.0049 22.0697 23.0682 22.1707 23.1119C22.2717 23.1555 22.3804 23.1786 22.4904 23.1797C22.6005 23.1809 22.7096 23.16 22.8115 23.1184C22.9134 23.0768 23.0059 23.0153 23.0837 22.9375C23.1616 22.8597 23.2231 22.7671 23.2646 22.6653C23.3062 22.5634 23.3271 22.4542 23.326 22.3442C23.3249 22.2342 23.3018 22.1254 23.2581 22.0244C23.2145 21.9234 23.1511 21.8321 23.0717 21.7559L19.5413 18.2255C20.3394 17.1815 20.8328 15.8936 20.8328 14.4834C20.8328 11.0673 18.0457 8.28027 14.6296 8.28027ZM14.6296 9.93444C17.1518 9.93444 19.1786 11.9613 19.1786 14.4834C19.1786 17.0055 17.1518 19.0324 14.6296 19.0324C12.1075 19.0324 10.0807 17.0055 10.0807 14.4834C10.0807 11.9613 12.1075 9.93444 14.6296 9.93444ZM12.5619 13.6563C12.4523 13.6548 12.3435 13.675 12.2418 13.7159C12.1401 13.7568 12.0475 13.8174 11.9695 13.8944C11.8914 13.9713 11.8295 14.063 11.7872 14.1642C11.7449 14.2653 11.7231 14.3738 11.7231 14.4834C11.7231 14.593 11.7449 14.7015 11.7872 14.8026C11.8295 14.9038 11.8914 14.9955 11.9695 15.0724C12.0475 15.1494 12.1401 15.2101 12.2418 15.2509C12.3435 15.2918 12.4523 15.312 12.5619 15.3105H16.6974C16.807 15.312 16.9158 15.2918 17.0175 15.2509C17.1192 15.2101 17.2117 15.1494 17.2898 15.0724C17.3678 14.9955 17.4298 14.9038 17.4721 14.8026C17.5144 14.7015 17.5362 14.593 17.5362 14.4834C17.5362 14.3738 17.5144 14.2653 17.4721 14.1642C17.4298 14.063 17.3678 13.9713 17.2898 13.8944C17.2117 13.8174 17.1192 13.7568 17.0175 13.7159C16.9158 13.675 16.807 13.6548 16.6974 13.6563H12.5619Z" fill="#EBECEF"/>
            </svg>
        </button>
      </div>
    </div>
  );
};
export default ChartToolbar;
