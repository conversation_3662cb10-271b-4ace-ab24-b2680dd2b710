import { ForwardedRef, forwardRef } from "react";

const CurvedLineSVG = forwardRef(
    (
      {
        path = "",
        color = "#fff",
        lineWidth = 1,
      }: {
        path?: string;
        color?: string;
        lineWidth?: number;
      },
      ref: ForwardedRef<any>
    ) => {
      // Generate SVG path for curved lines
  
      return (
        <svg className="z-10" width="100%" height="100%">
          <defs>
            <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
              <feFlood result="flood" floodColor="white" floodOpacity="1" />
              <feComposite in="flood" in2="SourceAlpha" operator="in" />
              <feGaussianBlur stdDeviation="5" />
              <feMerge>
                <feMergeNode />
                <feMergeNode in="SourceGraphic" />
              </feMerge>
            </filter>
          </defs>
          <path
            ref={ref}
            d={path}
            fill="none"
            stroke={color}
            strokeWidth={lineWidth}
            strokeLinecap="round"
            strokeLinejoin="round"
            filter="url(#glow)"
          />
        </svg>
      );
    }
  );

  export default CurvedLineSVG