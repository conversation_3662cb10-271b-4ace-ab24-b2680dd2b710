import classNames from "classnames";

interface Props {
  currentOrderTop: number;
  currentOrderPrice?: number;
  isLosing: boolean;
  zoomX: number;
}
const OrderLine: React.FC<Props> = ({
  currentOrderPrice,
  currentOrderTop,
  isLosing,
  zoomX,
}) => {
  return (
    <>
      {currentOrderTop > 0 && (
        <div
          className="w-full absolute h-[1px] z-99 border-t border-dashed border-blue-500 border-opacity-60  left-0"
          style={{ top: currentOrderTop }}
        >
          <div className="h-5 w-fit px-[5px] text-[11px] flex items-center justify-center rounded-full bg-blue-400 bg-opacity-10 backdrop-blur-sm -translate-y-1/2 right-[2%] absolute">
            {currentOrderPrice}
          </div>
          <div
            className={classNames(
              "h-5 w-fit px-[5px] text-[11px] flex items-center justify-center rounded-full  bg-opacity-20 backdrop-blur-sm -translate-y-1/2 right-[45%] translate-x-full absolute",
              { "bg-red-400 text-red-500": isLosing },
              { "bg-green-400 text-green-500": !isLosing }
            )}
          >
            {"+$10.2"}
          </div>
          <div
            className={
              "h-5 w-5 -translate-y-1/2 translate-x-1/2 flex items-center justify-center absolute rounded-full"
            }
            style={{ right: `${(1 - zoomX) * 100}%` }}
          >
            <div className="bg-blue-400 flex items-center justify-center w-full h-full rounded-full absolute bg-opacity-10 backdrop-blur-sm">
              {/* prettier-ignore */}
              <svg width="11" height="10" viewBox="0 0 11 10" fill="none">
                <g clipPath="url(#clip0_5417_1140)">
                <path d="M5.66483 0.659222C5.56066 0.660748 5.46136 0.703566 5.38875 0.778267C5.31613 0.852969 5.27614 0.953442 5.27757 1.05761V1.27753C3.64215 1.45979 2.33912 2.76282 2.15686 4.39824H1.93694C1.88486 4.3975 1.83316 4.40712 1.78483 4.42654C1.7365 4.44596 1.69251 4.47479 1.65543 4.51136C1.61834 4.54793 1.58889 4.5915 1.56878 4.63955C1.54868 4.6876 1.53833 4.73917 1.53833 4.79125C1.53833 4.84333 1.54868 4.8949 1.56878 4.94295C1.58889 4.991 1.61834 5.03457 1.65543 5.07114C1.69251 5.10771 1.7365 5.13654 1.78483 5.15596C1.83316 5.17538 1.88486 5.185 1.93694 5.18427H2.15686C2.33912 6.81968 3.64215 8.12271 5.27757 8.30497V8.52489C5.27683 8.57697 5.28645 8.62868 5.30587 8.677C5.32529 8.72533 5.35413 8.76932 5.39069 8.80641C5.42726 8.8435 5.47084 8.87295 5.51888 8.89305C5.56693 8.91315 5.6185 8.9235 5.67058 8.9235C5.72267 8.9235 5.77423 8.91315 5.82228 8.89305C5.87033 8.87295 5.9139 8.8435 5.95047 8.80641C5.98704 8.76932 6.01587 8.72533 6.03529 8.677C6.05471 8.62868 6.06433 8.57697 6.0636 8.52489V8.30497C7.69901 8.12271 9.00205 6.81968 9.1843 5.18427H9.40422C9.4563 5.185 9.50801 5.17538 9.55634 5.15596C9.60466 5.13654 9.64865 5.10771 9.68574 5.07114C9.72283 5.03457 9.75228 4.991 9.77238 4.94295C9.79248 4.8949 9.80284 4.84333 9.80284 4.79125C9.80284 4.73917 9.79248 4.6876 9.77238 4.63955C9.75228 4.5915 9.72283 4.54793 9.68574 4.51136C9.64865 4.47479 9.60466 4.44596 9.55634 4.42654C9.50801 4.40712 9.4563 4.3975 9.40422 4.39824H9.1843C9.00205 2.76282 7.69901 1.45979 6.0636 1.27753V1.05761C6.06432 1.00506 6.05449 0.952902 6.0347 0.904218C6.01491 0.855533 5.98556 0.811312 5.94838 0.774169C5.9112 0.737026 5.86695 0.707716 5.81825 0.687972C5.76954 0.668229 5.71737 0.658452 5.66483 0.659222ZM5.27757 2.07123V2.20633C5.27683 2.25841 5.28645 2.31012 5.30587 2.35845C5.32529 2.40678 5.35413 2.45076 5.39069 2.48785C5.42726 2.52494 5.47084 2.55439 5.51888 2.57449C5.56693 2.59459 5.6185 2.60495 5.67058 2.60495C5.72267 2.60495 5.77423 2.59459 5.82228 2.57449C5.87033 2.55439 5.9139 2.52494 5.95047 2.48785C5.98704 2.45076 6.01587 2.40678 6.03529 2.35845C6.05471 2.31012 6.06433 2.25841 6.0636 2.20633V2.07123C7.27099 2.24301 8.21882 3.19084 8.3906 4.39824H8.2555C8.20342 4.3975 8.15171 4.40712 8.10339 4.42654C8.05506 4.44596 8.01107 4.47479 7.97398 4.51136C7.93689 4.54793 7.90744 4.5915 7.88734 4.63955C7.86724 4.6876 7.85689 4.73917 7.85689 4.79125C7.85689 4.84333 7.86724 4.8949 7.88734 4.94295C7.90744 4.991 7.93689 5.03457 7.97398 5.07114C8.01107 5.10771 8.05506 5.13654 8.10339 5.15596C8.15171 5.17538 8.20342 5.185 8.2555 5.18427H8.3906C8.21882 6.39166 7.27099 7.33949 6.0636 7.51127V7.37617C6.06437 7.32359 6.05458 7.27139 6.03481 7.22266C6.01505 7.17393 5.9857 7.12966 5.94852 7.09248C5.91133 7.05529 5.86707 7.02595 5.81834 7.00618C5.76961 6.98641 5.71741 6.97662 5.66483 6.9774C5.61321 6.97815 5.56226 6.98906 5.51486 7.00951C5.46747 7.02996 5.42457 7.05955 5.38861 7.09658C5.35265 7.13361 5.32433 7.17736 5.30528 7.22533C5.28623 7.2733 5.27681 7.32456 5.27757 7.37617V7.51127C4.07018 7.33949 3.12234 6.39166 2.95057 5.18427H3.08567C3.13775 5.185 3.18945 5.17538 3.23778 5.15596C3.28611 5.13654 3.33009 5.10771 3.36718 5.07114C3.40427 5.03457 3.43372 4.991 3.45383 4.94295C3.47393 4.8949 3.48428 4.84333 3.48428 4.79125C3.48428 4.73917 3.47393 4.6876 3.45383 4.63955C3.43372 4.5915 3.40427 4.54793 3.36718 4.51136C3.33009 4.47479 3.28611 4.44596 3.23778 4.42654C3.18945 4.40712 3.13775 4.3975 3.08567 4.39824H2.95057C3.12234 3.19084 4.07018 2.24301 5.27757 2.07123ZM5.67058 4.00522C5.46211 4.00522 5.26219 4.08803 5.11478 4.23544C4.96737 4.38285 4.88455 4.58278 4.88455 4.79125C4.88455 4.99972 4.96737 5.19965 5.11478 5.34706C5.26219 5.49447 5.46211 5.57728 5.67058 5.57728C5.87905 5.57728 6.07898 5.49447 6.22639 5.34706C6.3738 5.19965 6.45661 4.99972 6.45661 4.79125C6.45661 4.58278 6.3738 4.38285 6.22639 4.23544C6.07898 4.08803 5.87905 4.00522 5.67058 4.00522Z" fill="#5AA2F7"/>
                </g>
                <defs>
                <clipPath id="clip0_5417_1140">
                <rect width="9.43235" height="9.43235" fill="white" transform="translate(0.954346 0.0751953)"/>
                </clipPath>
                </defs>
                </svg>
            </div>
          </div>
        </div>
      )}
    </>
  );
};
export default OrderLine;
