import { Button } from "@/components/ui/button";
import useClickOutside from "@/hooks/use-click-outside";
import useDebounce from "@/hooks/use-debounce";
import classNames from "classnames";
import { RefObject, useEffect, useRef, useState } from "react";
import { StoreRef } from "../utils/interfaces";

interface HandleProps {
  show?: boolean;
  aspectRatio: number;
  rootRef: React.RefObject<HTMLDivElement>;
  storeRef: RefObject<StoreRef>;
  startValue: number;
  invertHandle?: boolean;
  color?: "green" | "orange";
  onChange: (value: number) => void;
  deps?: any[];
}
export const ChartHandle: React.FC<HandleProps> = ({
  show = true,
  aspectRatio,
  rootRef,
  storeRef,
  startValue,
  invertHandle = false,
  color = "green",
  onChange,
  deps = [],
}) => {
  const [focused, setFocused] = useState(false);
  const [top, setTop] = useState(20);
  const [value, setValue] = useState(startValue);
  const [mouseDown, setMouseDown] = useState(false);

  useDebounce(value, 100, () => onChange(value));

  const {
    currentMax = 0,
    currentMin = 0,
    drawPoints = [],
  } = storeRef.current ?? {};

  const containerRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  useClickOutside(containerRef, () => {
    setFocused(false);
  });

  const width = containerRef.current?.clientWidth ?? 0;
  const height = width / aspectRatio;

  const valueDelta = currentMax - currentMin;
  const min = containerRef.current?.clientHeight ?? 0;

  useEffect(() => {
    if (containerRef.current) {
      if (value > 0) {
        const relativeTop = ((value - currentMin) / valueDelta) * height;
        setTop(relativeTop);
      }
    }
  }, [valueDelta, ...deps]);

  useEffect(() => {
    setValue(startValue);
    if (containerRef.current) {
      if (startValue > 0) {
        const relativeTop = ((startValue - currentMin) / valueDelta) * height;
        setTop(relativeTop);
      }
    }
  }, [startValue]);

  useEffect(() => {
    if (containerRef.current) {
      let mouseDown = false;

      const handleDown = () => {
        setMouseDown(true);
        mouseDown = true;
      };

      const handleUp = () => {
        setMouseDown(false);
        mouseDown = false;
      };

      const handleMove = (event: any) => {
        if (mouseDown) {
          event.stopPropagation();
          const dragY = event.y ?? event.touches[0].clientY;
          handleDelta(dragY);
        }
      };

      const handleDelta = (yPos: number) => {
        const rootRect = rootRef?.current?.getBoundingClientRect();
        const handleHeight = containerRef?.current?.clientHeight ?? 0;
        const viewHeight = height;

        const currentDrawY =
          storeRef.current?.currentOrderTop ??
          storeRef.current?.drawPoints?.[drawPoints.length - 1]?.y ??
          0;
        const maxYPos = invertHandle ? viewHeight - handleHeight : currentDrawY;
        const minYPos = invertHandle ? currentDrawY : min / 2;

        let relativeTop = yPos - ((rootRect?.y ?? 0) + handleHeight / 2);

        const atMin = relativeTop <= minYPos;
        const atMax = relativeTop >= maxYPos;
        if (atMin) {
          relativeTop = minYPos;
        } else if (atMax) {
          relativeTop = maxYPos;
        }
        const value = (
          currentMin +
          (relativeTop / viewHeight) * valueDelta
        ).toFixed(3);

        setValue(Number.parseFloat(value));
        setTop(relativeTop);
      };

      buttonRef.current?.addEventListener("mousedown", handleDown);
      buttonRef.current?.addEventListener("touchstart", handleDown);

      document.addEventListener("mouseup", handleUp);
      document.addEventListener("touchend", handleUp);

      document.addEventListener("mousemove", handleMove);
      document.addEventListener("touchmove", handleMove);

      return () => {
        buttonRef.current?.removeEventListener("mousedown", handleDown);
        buttonRef.current?.removeEventListener("touchstart", handleDown);

        document.removeEventListener("mouseup", handleUp);
        document.removeEventListener("touchend", handleUp);
        document.removeEventListener("mousemove", handleMove);
        document.removeEventListener("touchmove", handleMove);
      };
    }
  }, [valueDelta, startValue, ...deps]);

  return (
    <div
      ref={containerRef}
      className={classNames(
        `w-full flex items-center justify-center absolute h-[1px] hover:border-t-white cursor-pointer border-t border-dashed  border-opacity-40 z-10 left-0`,
        {
          hidden: !show,
          "z-[999] border-t-white border-opacity-70": focused,
          "border-t-orange-300": color === "orange",
          "border-t-green-300": color === "green",
        }
      )}
      style={{ top: isNaN(top) ? 0 : top }}
    >
      {mouseDown && (
        <div className="h-5 w-fit px-[5px] text-[11px] flex items-center justify-center rounded-full bg-white bg-opacity-10 backdrop-blur-sm -translate-y-[1.25px] right-[2%] absolute">
          {value}
        </div>
      )}
      <div
        className={classNames(
          "absolute w-full bg-gradient-to-b top-0 opacity-10 h-[50px] pointer-events-none",
          {
            "from-orange-300 to-transparent ":
              color === "orange" && !invertHandle,
            "from-green-300 to-transparent ":
              color === "green" && !invertHandle,

            "to-orange-300 from-transparent ":
              color === "orange" && invertHandle,
            "to-green-300 from-transparent ": color === "green" && invertHandle,
            "translate-y-[-100%]": invertHandle,
          }
        )}
      ></div>

      <Button
        onClick={() => setFocused(true)}
        ref={buttonRef}
        className={classNames(
          "-translate-y-[2.5px] h-[20px] py-0 hover:bg-white rounded-full",
          {
            "bg-orange-300": color === "orange",
            "bg-green-300": color === "green",
          }
        )}
      >
        {/* prettier-ignore */}
        <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g clipPath="url(#clip0_5417_1152)">
          <path d="M4.80474 0.928751C4.70252 0.93028 4.60491 0.971582 4.53263 1.04389L3.7466 1.82992C3.70888 1.86614 3.67876 1.90951 3.65802 1.95751C3.63727 2.00551 3.62631 2.05716 3.62578 2.10945C3.62525 2.16174 3.63516 2.21361 3.65492 2.26202C3.67469 2.31043 3.70391 2.35441 3.74088 2.39138C3.77786 2.42836 3.82184 2.45758 3.87025 2.47735C3.91866 2.49711 3.97053 2.50702 4.02281 2.50649C4.0751 2.50595 4.12676 2.49499 4.17476 2.47425C4.22275 2.4535 4.26613 2.42339 4.30235 2.38567L4.8105 1.87751L5.31866 2.38567C5.35487 2.42339 5.39825 2.4535 5.44625 2.47425C5.49424 2.49499 5.5459 2.50595 5.59819 2.50649C5.65047 2.50702 5.70234 2.49711 5.75075 2.47735C5.79916 2.45758 5.84314 2.42836 5.88012 2.39138C5.91709 2.35441 5.94632 2.31043 5.96608 2.26202C5.98585 2.21361 5.99575 2.16174 5.99522 2.10945C5.99469 2.05716 5.98373 2.00551 5.96298 1.95751C5.94224 1.90951 5.91212 1.86614 5.8744 1.82992L5.08837 1.04389C5.0512 1.0067 5.00695 0.977353 4.95823 0.957576C4.90951 0.937798 4.85732 0.927997 4.80474 0.928751ZM1.27337 3.09033C1.22129 3.0896 1.16958 3.09922 1.12125 3.11864C1.07293 3.13806 1.02894 3.16689 0.991851 3.20346C0.954762 3.24003 0.925311 3.2836 0.905209 3.33165C0.885108 3.3797 0.874756 3.43126 0.874756 3.48335C0.874756 3.53543 0.885108 3.587 0.905209 3.63505C0.925311 3.68309 0.954762 3.72667 0.991851 3.76324C1.02894 3.7998 1.07293 3.82864 1.12125 3.84806C1.16958 3.86748 1.22129 3.8771 1.27337 3.87636H8.34763C8.39971 3.8771 8.45142 3.86748 8.49975 3.84806C8.54808 3.82864 8.59206 3.7998 8.62915 3.76324C8.66624 3.72667 8.69569 3.68309 8.71579 3.63505C8.73589 3.587 8.74625 3.53543 8.74625 3.48335C8.74625 3.43126 8.73589 3.3797 8.71579 3.33165C8.69569 3.2836 8.66624 3.24003 8.62915 3.20346C8.59206 3.16689 8.54808 3.13806 8.49975 3.11864C8.45142 3.09922 8.39971 3.0896 8.34763 3.09033H1.27337ZM1.27337 4.46588C1.22129 4.46515 1.16958 4.47477 1.12125 4.49419C1.07293 4.51361 1.02894 4.54244 0.991851 4.57901C0.954762 4.61558 0.925311 4.65915 0.905209 4.7072C0.885108 4.75525 0.874756 4.80681 0.874756 4.8589C0.874756 4.91098 0.885108 4.96255 0.905209 5.0106C0.925311 5.05865 0.954762 5.10222 0.991851 5.13879C1.02894 5.17536 1.07293 5.20419 1.12125 5.22361C1.16958 5.24303 1.22129 5.25265 1.27337 5.25191H8.34763C8.39971 5.25265 8.45142 5.24303 8.49975 5.22361C8.54808 5.20419 8.59206 5.17536 8.62915 5.13879C8.66624 5.10222 8.69569 5.05865 8.71579 5.0106C8.73589 4.96255 8.74625 4.91098 8.74625 4.8589C8.74625 4.80681 8.73589 4.75525 8.71579 4.7072C8.69569 4.65915 8.66624 4.61558 8.62915 4.57901C8.59206 4.54244 8.54808 4.51361 8.49975 4.49419C8.45142 4.47477 8.39971 4.46515 8.34763 4.46588H1.27337ZM1.27337 5.84144C1.22129 5.8407 1.16958 5.85032 1.12125 5.86974C1.07293 5.88916 1.02894 5.91799 0.991851 5.95456C0.954762 5.99113 0.925311 6.0347 0.905209 6.08275C0.885108 6.1308 0.874756 6.18237 0.874756 6.23445C0.874756 6.28653 0.885108 6.3381 0.905209 6.38615C0.925311 6.4342 0.954762 6.47777 0.991851 6.51434C1.02894 6.55091 1.07293 6.57974 1.12125 6.59916C1.16958 6.61858 1.22129 6.6282 1.27337 6.62747H8.34763C8.39971 6.6282 8.45142 6.61858 8.49975 6.59916C8.54808 6.57974 8.59206 6.55091 8.62915 6.51434C8.66624 6.47777 8.69569 6.4342 8.71579 6.38615C8.73589 6.3381 8.74625 6.28653 8.74625 6.23445C8.74625 6.18237 8.73589 6.1308 8.71579 6.08275C8.69569 6.0347 8.66624 5.99113 8.62915 5.95456C8.59206 5.91799 8.54808 5.88916 8.49975 5.86974C8.45142 5.85032 8.39971 5.8407 8.34763 5.84144H1.27337ZM5.59308 7.21123C5.48922 7.21368 5.39056 7.25715 5.31866 7.33213L4.8105 7.84028L4.30235 7.33213C4.26568 7.29443 4.22181 7.26448 4.17336 7.24404C4.1249 7.22361 4.07284 7.2131 4.02025 7.21315C3.9421 7.21325 3.86575 7.23664 3.80096 7.28034C3.73617 7.32404 3.68587 7.38606 3.6565 7.45848C3.62713 7.5309 3.62001 7.61043 3.63606 7.68692C3.65211 7.7634 3.69059 7.83337 3.7466 7.88788L4.53263 8.67391C4.60633 8.74758 4.70628 8.78897 4.8105 8.78897C4.91472 8.78897 5.01467 8.74758 5.08837 8.67391L5.8744 7.88788C5.93159 7.83304 5.97087 7.7622 5.98709 7.68464C6.00331 7.60709 5.99572 7.52644 5.9653 7.45328C5.93488 7.38012 5.88306 7.31786 5.81664 7.27466C5.75021 7.23147 5.67229 7.20936 5.59308 7.21123Z" fill="#14171F"/>
          </g>
          <defs>
          <clipPath id="clip0_5417_1152">
          <rect width="9.43235" height="9.43235" fill="white" transform="translate(0.0942383 0.142578)"/>
          </clipPath>
          </defs>
          </svg>
      </Button>
    </div>
  );
};
