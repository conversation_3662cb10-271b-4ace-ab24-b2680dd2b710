export interface TickData {
  price: number;
  time: number;
}

export interface Point {
  x: number;
  y: number;
}

export interface Tick {
  price: number;
  time: number;
}

export interface StoreRef {
  currentMax?: number;
  currentMin?: number;
  drawPoints?: Point[];
  pricePoints?: {
    time: number;
    price: number;
  }[];
  dimensions?: {
    width: number;
    height: number;
  };
  currentPriceBucket?: { price: number; time: number }[];
  currentOrderTop?: number;
  tickCount?: number;
  redrawCount?: number;
  lastTickMinute?: number;
}

