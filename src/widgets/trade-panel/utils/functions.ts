import { StoreRef, Point, Tick } from "./interfaces";

export const createDrawPoints = (
  params: {
    pricePoints: Tick[];
    max: number;
    min: number;
    scaleY: number;
    zoomX: number;
    dimensions: StoreRef["dimensions"];
    isRedraw?: boolean;
  },
  incrementRedrawCount?: VoidFunction
): Point[] => {
  const {
    pricePoints: staticPoints,
    max,
    min,
    scaleY,
    zoomX,
    isRedraw = false,
    dimensions,
  } = params;
  
  if(!staticPoints) return [];

  if (isRedraw) incrementRedrawCount?.();

  const delta = max - min;
  const drawPoints = [] as Point[];
  const { height, width } = dimensions!;

  for (let i = 0; i < staticPoints.length; i++) {
    const { price } = staticPoints[i];
    const y = flipY(((price - min) / delta) * height, height);

    drawPoints.push({
      y: y * scaleY,
      x: (width / staticPoints.length) * (i * zoomX),
    });

    if (i === staticPoints.length - 1) {
      drawPoints.push({
        y: y * scaleY,
        x: (width / staticPoints.length) * ((i + 1) * zoomX),
      });
    }
  }

  if (staticPoints.length === 0) {
    drawPoints.push({
      y: height,
      x: 0,
    });
  }

  return drawPoints;
};

export const generateCurvePath = (points: Point[]) => {
  if (points.length < 2) return null;

  let path = `M ${points[0].x} ${points[0].y}`;

  for (let i = 1; i < points.length - 1; i++) {
    const xc = (points[i].x + points[i + 1].x) / 2;
    const yc = (points[i].y + points[i + 1].y) / 2;

    path += ` Q ${points[i].x} ${points[i].y}, ${xc} ${yc}`;
  }
  // Draw final segment
  path += ` L ${points[points.length - 1].x} ${points[points.length - 1].y}`;

  return path;
};

export const createStaticPoints = (params: {
  computeStaticMinMax?: boolean;
  ticks: Tick[];
  timeFrame: number;
  zoomY: number;
}) => {
  const { computeStaticMinMax = false, ticks, timeFrame, zoomY } = params;

  let max = 0;
  let min = Number.MAX_SAFE_INTEGER; // no number can be greater than this

  const pricePoints = [] as Tick[]; // final outputs

  const batch:any[] = []; // time frame batches

  for (let i = 0; i < ticks.length; i++) {
    const { price: tick, time } = ticks[i];

    // check if batch time has reached end
    // get secs from time (unix) and check if its mod div isn't 0
    const formattedDateTime = new Date(time * 1000);
    const minutes = formattedDateTime.getMinutes();

    // if we are in new frame -> | -- |
    if (minutes % timeFrame === 0) {
      if (batch?.length > 0) {
        // average last batch
        const avg = batch.reduce((a, b) => a + b, 0) / batch.length;
        // console.log(avg, batch.length);
        pricePoints.push({
          time: Math.round(time - timeFrame * 60),
          price: avg,
        });
        batch.splice(0, batch.length);
      }
      batch.push(tick);
    } else {
      batch.push(tick);
    }

    // push dummy tick
    batch.push(tick);

    // if we are using price data to calc min max
    if (computeStaticMinMax === false) {
      if (tick > max) max = tick;
      if (tick < min) min = tick;
    }

    // avg remaining batch if the loop is ending
    if (i === ticks.length - 1 && batch.length > 0) {
      const avg = batch.reduce((a, b) => a + b, 0) / batch.length;
      pricePoints.push({
        time: time + timeFrame,
        price: avg,
      });
    }
  }

  return {
    pricePoints,
    max,
    min,
    ...computePaddedMinMax(min, max, zoomY),
  };
};

export const flipY = (y: number, height: number) => {
  // return y
  return Math.abs(height - y);
};

export const toFixed = (num: number, points: number) => {
  return Number.parseFloat(num.toFixed(points));
};

export const computePaddedMinMax = (
  min: number,
  max: number,

  zoomY: number
) => {
  const delta = max - min;
  const padding = delta * zoomY;
  return { paddedMax: max + padding, paddedMin: min - padding };
};

const camelToSnake = (key: string): string =>
  key.replace(/([A-Z])/g, "_$1").toLowerCase();

export function toSnakeCaseKeys<T>(value: T): T {
  if (Array.isArray(value)) {
    // Map each element through the same transformer
    return value.map((v) => toSnakeCaseKeys(v)) as unknown as T;
  }

  if (value !== null && typeof value === "object" && !(value instanceof Date)) {
    const entries = Object.entries(value as Record<string, unknown>).map(
      ([k, v]) => [camelToSnake(k), toSnakeCaseKeys(v)]
    );

    // Preserve the original prototype (so class instances stay instances)
    return Object.assign(
      Object.create(Object.getPrototypeOf(value)),
      Object.fromEntries(entries)
    );
  }

  // Primitive (string, number, boolean, null, undefined, Date, etc.) – return as-is
  return value;
}
