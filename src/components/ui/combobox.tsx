"use client"

import { Check } from "lucide-react"
import * as React from "react"

import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { cn } from "@/lib/utils"

interface Props{
   items: {
    value: string
    label: string
  }[];
    value?: string;
    onChange?: (value: string) => void;
    classNames?: string;
    children?: React.ReactNode
}
const Combobox:React.FC<Props> =({items,onChange,value:val,children})=> {
  const [open, setOpen] = React.useState(false)
  const [value, setValue] = React.useState(val??"")

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        {children}
      </PopoverTrigger>
      <PopoverContent side="bottom" align="start" sideOffset={10}  className="w-[250px] p-0 rounded-xl overflow-hidden">
        <Command>
          <CommandInput className="h-[20px] text-xs" placeholder="Search Symbol..." />
          <CommandList>
            <CommandEmpty className="text-xs">No symbol found</CommandEmpty>
            <CommandGroup>
              {items.map((item) => (
                <CommandItem
                  key={item.value}
                  value={item.value}
                  onSelect={(currentValue) => {
                    onChange ?.(currentValue);
                    setValue(currentValue === value ? "" : currentValue)
                    setOpen(false)
                  }}
                  className="rounded-[8px] overflow-hidden text-xs"
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      value === item.value ? "opacity-100" : "opacity-0"
                    )}
                  />
                  {item.label}
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}


export default Combobox