import {
  CloseTradeRequest,
  GenericResponseType,
  GetAccountResponse,
  GetAvailableSymbolsResponse,
  GetOrdersResponse,
  GetPositionsResponse,
  GetSymbolTickRequest,
  GetTicksFromRequest,
  GetTicksRangeRequest,
  GetTicksResponse,
  GetTradeHistoryRequest,
  GetTradeHistoryResponse,
  ManageSymbolRequest,
  ManageSymbolResponse,
  ModifyTradeRequest,
  PlaceTradeRequest,
  PlaceTradeResponse,
  TickStartRequest,
} from "@/lib/bridge/bridge";
import { objectToParams, PromiseResult } from "@/lib/utils";
import { connectWebSocket, sendWsRequest } from "@/scripts/utils/functions";
import { toSnakeCaseKeys } from "@/widgets/trade-panel/utils/functions";
import { Tick } from "@/widgets/trade-panel/utils/interfaces";
import axios, { Axios } from "axios";
import { EventSource } from "eventsource";

class FxApiRepository {
  private static instance: FxApiRepository;
  private axios: Axios;
  private sseSources: { [key: string]: EventSource } = {} as any;
  private ws: WebSocket = null as any;

  private constructor(
    private apiKey: string,
    private server: string,
    private useWebsocket = false
  ) {
    this.axios = axios.create({
      baseURL: `http://${server}/api`,
      headers: {
        Authorization: `Bearer ${apiKey}`,
        "Content-Type": "application/json",
      },
      responseType: "json",
    });

    if (useWebsocket == true)
      connectWebSocket(`ws://${server}/api`)
        .then((ws) => {
          this.ws = ws;
        })
        .catch((e) => {
          this.useWebsocket = false;
          console.log(e);
        });
  }

  static getInstance() {
    if (this.instance) {
      return this.instance;
    }
    throw new Error("Not Instantiated");
  }

  static init(apiKey: string, server: string, useWebsocket = false) {
    FxApiRepository.instance = new FxApiRepository(
      apiKey,
      server,
      useWebsocket
    );
    return FxApiRepository.instance;
  }

  static registerSseListener(
    type: "tick" | "other",
    callback: (event: MessageEvent) => void
  ) {
    const instance = this.getInstance();
    if (instance) {
      const sse = instance.sseSources[type];
      if (sse) sse.onmessage = callback;
    }
  }

  // methods

  async getAccount(_: any): PromiseResult<GetAccountResponse> {
    try {
      const { data: res } = await this.axios.post("/get-account");
      return { data: res?.data };
    } catch (error: any) {
      return { error };
    }
  }

  async getTicksFrom(
    payload: GetTicksFromRequest
  ): PromiseResult<GetTicksResponse> {
    try {
      const { data: res } = await this.axios.post(
        "/get-ticks-from",
        toSnakeCaseKeys(payload)
      );
      return { data: res?.data };
    } catch (error: any) {
      return { error };
    }
  }

  async getTicksRange(
    payload: GetTicksRangeRequest
  ): PromiseResult<GetTicksResponse> {
    try {
      const { data: res } = await this.axios.post(
        "/get-ticks-range",
        toSnakeCaseKeys(payload)
      );
      return { data: res?.data };
    } catch (error: any) {
      return { error };
    }
  }

  async getPositions(_: any): PromiseResult<GetPositionsResponse> {
    try {
      const { data: res } = await this.axios.post("/get-positions");
      return { data: res?.data };
    } catch (error: any) {
      return { error };
    }
  }

  async getOrders(_: any): PromiseResult<GetOrdersResponse> {
    try {
      const { data: res } = await this.axios.post("/get-orders");
      return { data: res?.data };
    } catch (error: any) {
      return { error };
    }
  }

  async getSymbols(_: any): PromiseResult<GetAvailableSymbolsResponse> {
    try {
      const { data: res } = await this.axios.post("/get-symbols");
      return { data: res?.data };
    } catch (error: any) {
      return { error };
    }
  }

  async getTradeHistory(
    payload: GetTradeHistoryRequest
  ): PromiseResult<GetTradeHistoryResponse> {
    try {
      const { data: res } = await this.axios.post(
        "/get-trade-history",
        payload
      );
      return { data: res?.data };
    } catch (error: any) {
      return { error };
    }
  }

  async getSymbolTick(payload: GetSymbolTickRequest): PromiseResult<Tick> {
    try {
      const { data: res } = await this.axios.post(
        "/get-symbol-tick",
        toSnakeCaseKeys(payload)
      );
      return { data: res?.data };
    } catch (error: any) {
      return { error };
    }
  }

  async placeTrade(
    payload: PlaceTradeRequest
  ): PromiseResult<PlaceTradeResponse> {
    try {
      if (this.useWebsocket) {
        const data = await sendWsRequest(
          "place-trade",
          this.ws,
          toSnakeCaseKeys(payload)
        );
        return { data };
      }
      const { data: res } = await this.axios.post(
        "/place-trade",
        toSnakeCaseKeys(payload)
      );
      return { data: res?.data };
    } catch (error: any) {
      return { error };
    }
  }

  async modifyTrade(
    payload: ModifyTradeRequest
  ): PromiseResult<GenericResponseType> {
    try {
      if (this.useWebsocket) {
        const data = await sendWsRequest(
          "modify-trade",
          this.ws,
          toSnakeCaseKeys(payload)
        );
        return { data };
      }
      const { data: res } = await this.axios.post(
        "/modify-trade",
        toSnakeCaseKeys(payload)
      );
      return { data: res?.data };
    } catch (error: any) {
      return { error };
    }
  }

  async closeTrade(
    payload: CloseTradeRequest
  ): PromiseResult<GenericResponseType> {
    try {
      if (this.useWebsocket) {
        const data = await sendWsRequest(
          "close-trade",
          this.ws,
          toSnakeCaseKeys(payload)
        );
        return { data };
      }
      const { data: res } = await this.axios.post(
        "/close-trade",
        toSnakeCaseKeys(payload)
      );
      return { data: res?.data };
    } catch (error: any) {
      return { error };
    }
  }

  async manageSymbol(
    payload: ManageSymbolRequest
  ): PromiseResult<ManageSymbolResponse> {
    try {
      const { data: res } = await this.axios.post(
        "/manage-symbol",
        toSnakeCaseKeys(payload)
      );
      return { data: res?.data };
    } catch (error: any) {
      return { error };
    }
  }

  async getError(_: any): PromiseResult<GenericResponseType> {
    try {
      const { data: res } = await this.axios.post("/get-error");
      return { data: res?.data };
    } catch (error: any) {
      return { error };
    }
  }

  async tickStart(payload: TickStartRequest): PromiseResult<void> {
    const params = objectToParams(payload);
    this.sseSources["tick"] = new EventSource(
      `http://${this.server}/api/tick-start?${params}`,
      {
        fetch: (input, init) =>
          fetch(input, {
            ...init,
            headers: {
              ...init?.headers,
              Authorization: `Bearer ${this.apiKey}`,
            },
          }),
      }
    );
    return {};
  }

  async tickStop(_: any): PromiseResult<void> {
    this.sseSources["tick"]?.close();
    try {
      const { data: res } = await this.axios.post("/tick-stop");
      return { data: res?.data };
    } catch (error: any) {
      return { error };
    }
  }
}

export default FxApiRepository;

export type FxApiRepoMethods = keyof FxApiRepository;
